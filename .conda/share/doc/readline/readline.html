<!DOCTYPE html>
<html>
<!-- Created by GNU Texinfo 7.1, https://www.gnu.org/software/texinfo/ -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<!-- This manual describes the GNU Readline Library
(version 8.3, 30 December 2024), a library which aids in the
consistency of user interface across discrete programs which provide
a command line interface.

Copyright © 1988-2025 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with no
Invariant Sections, no Front-Cover Texts, and no Back-Cover Texts.
A copy of the license is included in the section entitled
"GNU Free Documentation License".
 -->
<title>GNU Readline Library</title>

<meta name="description" content="GNU Readline Library">
<meta name="keywords" content="GNU Readline Library">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta name="viewport" content="width=device-width,initial-scale=1">

<link href="#Top" rel="start" title="Top">
<link href="#Concept-Index" rel="index" title="Concept Index">
<link href="#SEC_Contents" rel="contents" title="Table of Contents">
<link href="#Command-Line-Editing" rel="next" title="Command Line Editing">
<style type="text/css">
<!--
a.copiable-link {visibility: hidden; text-decoration: none; line-height: 0em}
a.summary-letter-printindex {text-decoration: none}
div.center {text-align:center}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
kbd.kbd {font-style: oblique}
kbd.key {font-style: normal}
pre.display-preformatted {font-family: inherit}
span:hover a.copiable-link {visibility: visible}
strong.def-name {font-family: monospace; font-weight: bold; font-size: larger}
td.printindex-index-entry {vertical-align: top}
td.printindex-index-section {vertical-align: top; padding-left: 1em}
th.entries-header-printindex {text-align:left}
th.sections-header-printindex {text-align:left; padding-left: 1em}
ul.mark-bullet {list-style-type: disc}
ul.toc-numbered-mark {list-style: none}
-->
</style>


</head>

<body lang="en">









<div class="top-level-extent" id="Top">
<div class="nav-panel">
<p>
Next: <a href="#Command-Line-Editing" accesskey="n" rel="next">Command Line Editing</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h1 class="top" id="GNU-Readline-Library"><span>GNU Readline Library<a class="copiable-link" href="#GNU-Readline-Library"> &para;</a></span></h1>

<p>This document describes the GNU Readline Library, a utility which aids
in the consistency of user interface across discrete programs which
provide a command line interface.
The Readline home page is <a class="url" href="http://www.gnu.org/software/readline/">http://www.gnu.org/software/readline/</a>.
</p>





<div class="element-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Command-Line-Editing-1" href="#Command-Line-Editing">1 Command Line Editing</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Introduction-to-Line-Editing" href="#Introduction-and-Notation">1.1 Introduction to Line Editing</a></li>
    <li><a id="toc-Readline-Interaction-1" href="#Readline-Interaction">1.2 Readline Interaction</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Readline-Bare-Essentials-1" href="#Readline-Bare-Essentials">1.2.1 Readline Bare Essentials</a></li>
      <li><a id="toc-Readline-Movement-Commands-1" href="#Readline-Movement-Commands">1.2.2 Readline Movement Commands</a></li>
      <li><a id="toc-Readline-Killing-Commands-1" href="#Readline-Killing-Commands">1.2.3 Readline Killing Commands</a></li>
      <li><a id="toc-Readline-Arguments-1" href="#Readline-Arguments">1.2.4 Readline Arguments</a></li>
      <li><a id="toc-Searching-for-Commands-in-the-History" href="#Searching">1.2.5 Searching for Commands in the History</a></li>
    </ul></li>
    <li><a id="toc-Readline-Init-File-1" href="#Readline-Init-File">1.3 Readline Init File</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Readline-Init-File-Syntax-1" href="#Readline-Init-File-Syntax">1.3.1 Readline Init File Syntax</a></li>
      <li><a id="toc-Conditional-Init-Constructs-1" href="#Conditional-Init-Constructs">1.3.2 Conditional Init Constructs</a></li>
      <li><a id="toc-Sample-Init-File-1" href="#Sample-Init-File">1.3.3 Sample Init File</a></li>
    </ul></li>
    <li><a id="toc-Bindable-Readline-Commands-1" href="#Bindable-Readline-Commands">1.4 Bindable Readline Commands</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Commands-For-Moving-1" href="#Commands-For-Moving">1.4.1 Commands For Moving</a></li>
      <li><a id="toc-Commands-For-Manipulating-The-History" href="#Commands-For-History">1.4.2 Commands For Manipulating The History</a></li>
      <li><a id="toc-Commands-For-Changing-Text" href="#Commands-For-Text">1.4.3 Commands For Changing Text</a></li>
      <li><a id="toc-Killing-And-Yanking" href="#Commands-For-Killing">1.4.4 Killing And Yanking</a></li>
      <li><a id="toc-Specifying-Numeric-Arguments" href="#Numeric-Arguments">1.4.5 Specifying Numeric Arguments</a></li>
      <li><a id="toc-Letting-Readline-Type-For-You" href="#Commands-For-Completion">1.4.6 Letting Readline Type For You</a></li>
      <li><a id="toc-Keyboard-Macros-1" href="#Keyboard-Macros">1.4.7 Keyboard Macros</a></li>
      <li><a id="toc-Some-Miscellaneous-Commands" href="#Miscellaneous-Commands">1.4.8 Some Miscellaneous Commands</a></li>
    </ul></li>
    <li><a id="toc-Readline-vi-Mode-1" href="#Readline-vi-Mode">1.5 Readline vi Mode</a></li>
  </ul></li>
  <li><a id="toc-Programming-with-GNU-Readline-1" href="#Programming-with-GNU-Readline">2 Programming with GNU Readline</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Basic-Behavior-1" href="#Basic-Behavior">2.1 Basic Behavior</a></li>
    <li><a id="toc-Custom-Functions-1" href="#Custom-Functions">2.2 Custom Functions</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Readline-Typedefs-1" href="#Readline-Typedefs">2.2.1 Readline Typedefs</a></li>
      <li><a id="toc-Writing-a-New-Function" href="#Function-Writing">2.2.2 Writing a New Function</a></li>
    </ul></li>
    <li><a id="toc-Readline-Variables-1" href="#Readline-Variables">2.3 Readline Variables</a></li>
    <li><a id="toc-Readline-Convenience-Functions-1" href="#Readline-Convenience-Functions">2.4 Readline Convenience Functions</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Naming-a-Function" href="#Function-Naming">2.4.1 Naming a Function</a></li>
      <li><a id="toc-Selecting-a-Keymap" href="#Keymaps">2.4.2 Selecting a Keymap</a></li>
      <li><a id="toc-Binding-Keys-1" href="#Binding-Keys">2.4.3 Binding Keys</a></li>
      <li><a id="toc-Associating-Function-Names-and-Bindings-1" href="#Associating-Function-Names-and-Bindings">2.4.4 Associating Function Names and Bindings</a></li>
      <li><a id="toc-Allowing-Undoing-1" href="#Allowing-Undoing">2.4.5 Allowing Undoing</a></li>
      <li><a id="toc-Redisplay-1" href="#Redisplay">2.4.6 Redisplay</a></li>
      <li><a id="toc-Modifying-Text-1" href="#Modifying-Text">2.4.7 Modifying Text</a></li>
      <li><a id="toc-Character-Input-1" href="#Character-Input">2.4.8 Character Input</a></li>
      <li><a id="toc-Terminal-Management-1" href="#Terminal-Management">2.4.9 Terminal Management</a></li>
      <li><a id="toc-Utility-Functions-1" href="#Utility-Functions">2.4.10 Utility Functions</a></li>
      <li><a id="toc-Miscellaneous-Functions-1" href="#Miscellaneous-Functions">2.4.11 Miscellaneous Functions</a></li>
      <li><a id="toc-Alternate-Interface-1" href="#Alternate-Interface">2.4.12 Alternate Interface</a></li>
      <li><a id="toc-A-Readline-Example-1" href="#A-Readline-Example">2.4.13 A Readline Example</a></li>
      <li><a id="toc-Alternate-Interface-Example-1" href="#Alternate-Interface-Example">2.4.14 Alternate Interface Example</a></li>
    </ul></li>
    <li><a id="toc-Readline-Signal-Handling-1" href="#Readline-Signal-Handling">2.5 Readline Signal Handling</a></li>
    <li><a id="toc-Custom-Completers-1" href="#Custom-Completers">2.6 Custom Completers</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-How-Completing-Works-1" href="#How-Completing-Works">2.6.1 How Completing Works</a></li>
      <li><a id="toc-Completion-Functions-1" href="#Completion-Functions">2.6.2 Completion Functions</a></li>
      <li><a id="toc-Completion-Variables-1" href="#Completion-Variables">2.6.3 Completion Variables</a></li>
      <li><a id="toc-A-Short-Completion-Example-1" href="#A-Short-Completion-Example">2.6.4 A Short Completion Example</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-GNU-Free-Documentation-License-1" href="#GNU-Free-Documentation-License">Appendix A GNU Free Documentation License</a></li>
  <li><a id="toc-Concept-Index-1" href="#Concept-Index" rel="index">Concept Index</a></li>
  <li><a id="toc-Function-and-Variable-Index-1" href="#Function-and-Variable-Index" rel="index">Function and Variable Index</a></li>
</ul>
</div>
</div>
<hr>
<div class="chapter-level-extent" id="Command-Line-Editing">
<div class="nav-panel">
<p>
Next: <a href="#Programming-with-GNU-Readline" accesskey="n" rel="next">Programming with GNU Readline</a>, Previous: <a href="#Top" accesskey="p" rel="prev">GNU Readline Library</a>, Up: <a href="#Top" accesskey="u" rel="up">GNU Readline Library</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h2 class="chapter" id="Command-Line-Editing-1"><span>1 Command Line Editing<a class="copiable-link" href="#Command-Line-Editing-1"> &para;</a></span></h2>

<p>This chapter describes the basic features of the <small class="sc">GNU</small>
command line editing interface.
</p>

<ul class="mini-toc">
<li><a href="#Introduction-and-Notation" accesskey="1">Introduction to Line Editing</a></li>
<li><a href="#Readline-Interaction" accesskey="2">Readline Interaction</a></li>
<li><a href="#Readline-Init-File" accesskey="3">Readline Init File</a></li>
<li><a href="#Bindable-Readline-Commands" accesskey="4">Bindable Readline Commands</a></li>
<li><a href="#Readline-vi-Mode" accesskey="5">Readline vi Mode</a></li>
</ul>
<hr>
<div class="section-level-extent" id="Introduction-and-Notation">
<div class="nav-panel">
<p>
Next: <a href="#Readline-Interaction" accesskey="n" rel="next">Readline Interaction</a>, Up: <a href="#Command-Line-Editing" accesskey="u" rel="up">Command Line Editing</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h3 class="section" id="Introduction-to-Line-Editing"><span>1.1 Introduction to Line Editing<a class="copiable-link" href="#Introduction-to-Line-Editing"> &para;</a></span></h3>

<p>The following paragraphs use Emacs style to
describe the notation used to represent keystrokes.
</p>
<p>The text <kbd class="kbd">C-k</kbd> is read as &lsquo;Control-K&rsquo; and describes the character
produced when the <kbd class="key">k</kbd> key is pressed while the Control key
is depressed.
</p>
<p>The text <kbd class="kbd">M-k</kbd> is read as &lsquo;Meta-K&rsquo; and describes the character
produced when the Meta key (if you have one) is depressed, and the <kbd class="key">k</kbd>
key is pressed (a <em class="dfn">meta character</em>), then both are released.
The Meta key is labeled <kbd class="key">ALT</kbd> or <kbd class="key">Option</kbd> on many keyboards.
On keyboards with two keys labeled <kbd class="key">ALT</kbd> (usually to either side of
the space bar), the <kbd class="key">ALT</kbd> on the left side is generally set to
work as a Meta key.
One of the <kbd class="key">ALT</kbd> keys may also be configured
as some other modifier, such as a
Compose key for typing accented characters.
</p>
<p>On some keyboards, the Meta key modifier produces characters with
the eighth bit (0200) set.
You can use the <code class="code">enable-meta-key</code> variable
to control whether or not it does this, if the keyboard allows it.
On many others, the terminal or terminal emulator converts the metafied
key to a key sequence beginning with <kbd class="key">ESC</kbd> as described in the
next paragraph.
</p>
<p>If you do not have a Meta or <kbd class="key">ALT</kbd> key, or another key working as
a Meta key, you can generally achieve the latter effect by typing <kbd class="key">ESC</kbd>
<em class="emph">first</em>, and then typing <kbd class="key">k</kbd>.
The <kbd class="key">ESC</kbd> character is known as the <em class="dfn">meta prefix</em>).
</p>
<p>Either process is known as <em class="dfn">metafying</em> the <kbd class="key">k</kbd> key.
</p>
<p>If your Meta key produces a key sequence with the <kbd class="key">ESC</kbd> meta prefix,
you can make <kbd class="kbd">M-key</kbd> key bindings you specify
(see <code class="code">Key Bindings</code> in <a class="ref" href="#Readline-Init-File-Syntax">Readline Init File Syntax</a>)
do the same thing by setting the <code class="code">force-meta-prefix</code> variable.
</p>
<p>The text <kbd class="kbd">M-C-k</kbd> is read as &lsquo;Meta-Control-k&rsquo; and describes the
character produced by metafying <kbd class="kbd">C-k</kbd>.
</p>
<p>In addition, several keys have their own names.
Specifically,
<kbd class="key">DEL</kbd>, <kbd class="key">ESC</kbd>, <kbd class="key">LFD</kbd>, <kbd class="key">SPC</kbd>, <kbd class="key">RET</kbd>, and <kbd class="key">TAB</kbd> all
stand for themselves when seen in this text, or in an init file
(see <a class="pxref" href="#Readline-Init-File">Readline Init File</a>).
If your keyboard lacks a <kbd class="key">LFD</kbd> key, typing <kbd class="key">C-j</kbd> will
output the appropriate character.
The <kbd class="key">RET</kbd> key may be labeled <kbd class="key">Return</kbd> or <kbd class="key">Enter</kbd> on
some keyboards.
</p>
<hr>
</div>
<div class="section-level-extent" id="Readline-Interaction">
<div class="nav-panel">
<p>
Next: <a href="#Readline-Init-File" accesskey="n" rel="next">Readline Init File</a>, Previous: <a href="#Introduction-and-Notation" accesskey="p" rel="prev">Introduction to Line Editing</a>, Up: <a href="#Command-Line-Editing" accesskey="u" rel="up">Command Line Editing</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h3 class="section" id="Readline-Interaction-1"><span>1.2 Readline Interaction<a class="copiable-link" href="#Readline-Interaction-1"> &para;</a></span></h3>
<a class="index-entry-id" id="index-interaction_002c-readline"></a>

<p>Often during an interactive session you type in a long line of text,
only to notice that the first word on the line is misspelled.
The Readline library gives you a set of commands for manipulating the text
as you type it in, allowing you to just fix your typo, and not forcing
you to retype the majority of the line.
Using these editing commands,
you move the cursor to the place that needs correction, and delete or
insert the text of the corrections.
Then, when you are satisfied with the line, you simply press <kbd class="key">RET</kbd>.
You do not have to be at the
end of the line to press <kbd class="key">RET</kbd>; the entire line is accepted
regardless of the location of the cursor within the line.
</p>

<ul class="mini-toc">
<li><a href="#Readline-Bare-Essentials" accesskey="1">Readline Bare Essentials</a></li>
<li><a href="#Readline-Movement-Commands" accesskey="2">Readline Movement Commands</a></li>
<li><a href="#Readline-Killing-Commands" accesskey="3">Readline Killing Commands</a></li>
<li><a href="#Readline-Arguments" accesskey="4">Readline Arguments</a></li>
<li><a href="#Searching" accesskey="5">Searching for Commands in the History</a></li>
</ul>
<hr>
<div class="subsection-level-extent" id="Readline-Bare-Essentials">
<div class="nav-panel">
<p>
Next: <a href="#Readline-Movement-Commands" accesskey="n" rel="next">Readline Movement Commands</a>, Up: <a href="#Readline-Interaction" accesskey="u" rel="up">Readline Interaction</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Readline-Bare-Essentials-1"><span>1.2.1 Readline Bare Essentials<a class="copiable-link" href="#Readline-Bare-Essentials-1"> &para;</a></span></h4>
<a class="index-entry-id" id="index-notation_002c-readline"></a>
<a class="index-entry-id" id="index-command-editing"></a>
<a class="index-entry-id" id="index-editing-command-lines"></a>

<p>In order to enter characters into the line, simply type them.
The typed
character appears where the cursor was, and then the cursor moves one
space to the right.
If you mistype a character, you can use your
erase character to back up and delete the mistyped character.
</p>
<p>Sometimes you may mistype a character, and
not notice the error until you have typed several other characters.
In that case, you can type <kbd class="kbd">C-b</kbd> to move the cursor to the left,
and then correct your mistake.
Afterwards, you can move the cursor to the right with <kbd class="kbd">C-f</kbd>.
</p>
<p>When you add text in the middle of a line, you will notice that characters
to the right of the cursor are &lsquo;pushed over&rsquo; to make room for the text
that you have inserted.
Likewise, when you delete text behind the cursor,
characters to the right of the cursor are &lsquo;pulled back&rsquo; to fill in the
blank space created by the removal of the text.
These are the bare
essentials for editing the text of an input line:
</p>
<dl class="table">
<dt><kbd class="kbd">C-b</kbd></dt>
<dd><p>Move back one character.
</p></dd>
<dt><kbd class="kbd">C-f</kbd></dt>
<dd><p>Move forward one character.
</p></dd>
<dt><kbd class="key">DEL</kbd> or <kbd class="key">Backspace</kbd></dt>
<dd><p>Delete the character to the left of the cursor.
</p></dd>
<dt><kbd class="kbd">C-d</kbd></dt>
<dd><p>Delete the character underneath the cursor.
</p></dd>
<dt>Printing&nbsp;characters<!-- /@w --></dt>
<dd><p>Insert the character into the line at the cursor.
</p></dd>
<dt><kbd class="kbd">C-_</kbd> or <kbd class="kbd">C-x C-u</kbd></dt>
<dd><p>Undo the last editing command.
You can undo all the way back to an empty line.
</p></dd>
</dl>

<p>Depending on your configuration, the <kbd class="key">Backspace</kbd> key might be set to
delete the character to the left of the cursor and the <kbd class="key">DEL</kbd> key set
to delete the character underneath the cursor, like <kbd class="kbd">C-d</kbd>, rather
than the character to the left of the cursor.
</p>
<hr>
</div>
<div class="subsection-level-extent" id="Readline-Movement-Commands">
<div class="nav-panel">
<p>
Next: <a href="#Readline-Killing-Commands" accesskey="n" rel="next">Readline Killing Commands</a>, Previous: <a href="#Readline-Bare-Essentials" accesskey="p" rel="prev">Readline Bare Essentials</a>, Up: <a href="#Readline-Interaction" accesskey="u" rel="up">Readline Interaction</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Readline-Movement-Commands-1"><span>1.2.2 Readline Movement Commands<a class="copiable-link" href="#Readline-Movement-Commands-1"> &para;</a></span></h4>

<p>The above table describes the most basic keystrokes that you need
in order to do editing of the input line.
For your convenience, many other commands are available in
addition to <kbd class="kbd">C-b</kbd>, <kbd class="kbd">C-f</kbd>, <kbd class="kbd">C-d</kbd>, and <kbd class="key">DEL</kbd>.
Here are some commands for moving more rapidly within the line.
</p>
<dl class="table">
<dt><kbd class="kbd">C-a</kbd></dt>
<dd><p>Move to the start of the line.
</p></dd>
<dt><kbd class="kbd">C-e</kbd></dt>
<dd><p>Move to the end of the line.
</p></dd>
<dt><kbd class="kbd">M-f</kbd></dt>
<dd><p>Move forward a word, where a word is composed of letters and digits.
</p></dd>
<dt><kbd class="kbd">M-b</kbd></dt>
<dd><p>Move backward a word.
</p></dd>
<dt><kbd class="kbd">C-l</kbd></dt>
<dd><p>Clear the screen, reprinting the current line at the top.
</p></dd>
</dl>

<p>Notice how <kbd class="kbd">C-f</kbd> moves forward a character, while <kbd class="kbd">M-f</kbd> moves
forward a word.
It is a loose convention that control keystrokes
operate on characters while meta keystrokes operate on words.
</p>
<hr>
</div>
<div class="subsection-level-extent" id="Readline-Killing-Commands">
<div class="nav-panel">
<p>
Next: <a href="#Readline-Arguments" accesskey="n" rel="next">Readline Arguments</a>, Previous: <a href="#Readline-Movement-Commands" accesskey="p" rel="prev">Readline Movement Commands</a>, Up: <a href="#Readline-Interaction" accesskey="u" rel="up">Readline Interaction</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Readline-Killing-Commands-1"><span>1.2.3 Readline Killing Commands<a class="copiable-link" href="#Readline-Killing-Commands-1"> &para;</a></span></h4>

<a class="index-entry-id" id="index-killing-text"></a>
<a class="index-entry-id" id="index-yanking-text"></a>

<p><em class="dfn">Killing</em> text means to delete the text from the line, but to save
it away for later use, usually by <em class="dfn">yanking</em> (re-inserting)
it back into the line.
(&lsquo;Cut&rsquo; and &lsquo;paste&rsquo; are more recent jargon for &lsquo;kill&rsquo; and &lsquo;yank&rsquo;.)
</p>
<p>If the description for a command says that it &lsquo;kills&rsquo; text, then you can
be sure that you can get the text back in a different (or the same)
place later.
</p>
<p>When you use a kill command, the text is saved in a <em class="dfn">kill-ring</em>.
Any number of consecutive kills save all of the killed text together, so
that when you yank it back, you get it all.
The kill ring is not line specific; the text that you killed on a previously
typed line is available to be yanked back later, when you are typing
another line.
<a class="index-entry-id" id="index-kill-ring"></a>
</p>
<p>Here is the list of commands for killing text.
</p>
<dl class="table">
<dt><kbd class="kbd">C-k</kbd></dt>
<dd><p>Kill the text from the current cursor position to the end of the line.
</p>
</dd>
<dt><kbd class="kbd">M-d</kbd></dt>
<dd><p>Kill from the cursor to the end of the current word, or, if between
words, to the end of the next word.
Word boundaries are the same as those used by <kbd class="kbd">M-f</kbd>.
</p>
</dd>
<dt><kbd class="kbd">M-<kbd class="key">DEL</kbd></kbd></dt>
<dd><p>Kill from the cursor to the start of the current word, or, if between
words, to the start of the previous word.
Word boundaries are the same as those used by <kbd class="kbd">M-b</kbd>.
</p>
</dd>
<dt><kbd class="kbd">C-w</kbd></dt>
<dd><p>Kill from the cursor to the previous whitespace.
This is different than
<kbd class="kbd">M-<kbd class="key">DEL</kbd></kbd> because the word boundaries differ.
</p>
</dd>
</dl>

<p>Here is how to <em class="dfn">yank</em> the text back into the line.  Yanking
means to copy the most-recently-killed text from the kill buffer
into the line at the current cursor position.
</p>
<dl class="table">
<dt><kbd class="kbd">C-y</kbd></dt>
<dd><p>Yank the most recently killed text back into the buffer at the cursor.
</p>
</dd>
<dt><kbd class="kbd">M-y</kbd></dt>
<dd><p>Rotate the kill-ring, and yank the new top.
You can only do this if the prior command is <kbd class="kbd">C-y</kbd> or <kbd class="kbd">M-y</kbd>.
</p></dd>
</dl>

<hr>
</div>
<div class="subsection-level-extent" id="Readline-Arguments">
<div class="nav-panel">
<p>
Next: <a href="#Searching" accesskey="n" rel="next">Searching for Commands in the History</a>, Previous: <a href="#Readline-Killing-Commands" accesskey="p" rel="prev">Readline Killing Commands</a>, Up: <a href="#Readline-Interaction" accesskey="u" rel="up">Readline Interaction</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Readline-Arguments-1"><span>1.2.4 Readline Arguments<a class="copiable-link" href="#Readline-Arguments-1"> &para;</a></span></h4>

<p>You can pass numeric arguments to Readline commands.
Sometimes the
argument acts as a repeat count, other times it is the <i class="i">sign</i> of the
argument that is significant.
If you pass a negative argument to a
command which normally acts in a forward direction, that command will
act in a backward direction.
For example, to kill text back to the
start of the line, you might type &lsquo;<samp class="samp">M-- C-k</samp>&rsquo;.
</p>
<p>The general way to pass numeric arguments to a command is to type meta
digits before the command.
If the first &lsquo;digit&rsquo; typed is a minus
sign (&lsquo;<samp class="samp">-</samp>&rsquo;), then the sign of the argument will be negative.
Once you have typed one meta digit to get the argument started, you can
type the remainder of the digits, and then the command.
For example, to give
the <kbd class="kbd">C-d</kbd> command an argument of 10, you could type &lsquo;<samp class="samp">M-1 0 C-d</samp>&rsquo;,
which will delete the next ten characters on the input line.
</p>
<hr>
</div>
<div class="subsection-level-extent" id="Searching">
<div class="nav-panel">
<p>
Previous: <a href="#Readline-Arguments" accesskey="p" rel="prev">Readline Arguments</a>, Up: <a href="#Readline-Interaction" accesskey="u" rel="up">Readline Interaction</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Searching-for-Commands-in-the-History"><span>1.2.5 Searching for Commands in the History<a class="copiable-link" href="#Searching-for-Commands-in-the-History"> &para;</a></span></h4>

<p>Readline provides commands for searching through the command history
for lines containing a specified string.
There are two search modes:  <em class="dfn">incremental</em> and <em class="dfn">non-incremental</em>.
</p>
<p>Incremental searches begin before the user has finished typing the
search string.
As each character of the search string is typed, Readline displays
the next entry from the history matching the string typed so far.
An incremental search requires only as many characters as needed to
find the desired history entry.
When using emacs editing mode, type <kbd class="kbd">C-r</kbd>
to search backward in the history for a particular string.
Typing <kbd class="kbd">C-s</kbd> searches forward through the history.
The characters present in the value of the <code class="code">isearch-terminators</code> variable
are used to terminate an incremental search.
If that variable has not been assigned a value, the <kbd class="key">ESC</kbd> and
<kbd class="kbd">C-j</kbd> characters terminate an incremental search.
<kbd class="kbd">C-g</kbd> aborts an incremental search and restores the original line.
When the search is terminated, the history entry containing the
search string becomes the current line.
</p>
<p>To find other matching entries in the history list, type <kbd class="kbd">C-r</kbd> or
<kbd class="kbd">C-s</kbd> as appropriate.
This searches backward or forward in the history for the next
entry matching the search string typed so far.
Any other key sequence bound to a Readline command terminates
the search and executes that command.
For instance, a <kbd class="key">RET</kbd> terminates the search and accepts
the line, thereby executing the command from the history list.
A movement command will terminate the search, make the last line found
the current line, and begin editing.
</p>
<p>Readline remembers the last incremental search string.
If two <kbd class="kbd">C-r</kbd>s are typed without any intervening characters defining
a new search string, Readline uses any remembered search string.
</p>
<p>Non-incremental searches read the entire search string before starting
to search for matching history entries.
The search string may be typed by the user or be part of the contents of
the current line.
</p>
<hr>
</div>
</div>
<div class="section-level-extent" id="Readline-Init-File">
<div class="nav-panel">
<p>
Next: <a href="#Bindable-Readline-Commands" accesskey="n" rel="next">Bindable Readline Commands</a>, Previous: <a href="#Readline-Interaction" accesskey="p" rel="prev">Readline Interaction</a>, Up: <a href="#Command-Line-Editing" accesskey="u" rel="up">Command Line Editing</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h3 class="section" id="Readline-Init-File-1"><span>1.3 Readline Init File<a class="copiable-link" href="#Readline-Init-File-1"> &para;</a></span></h3>
<a class="index-entry-id" id="index-initialization-file_002c-readline"></a>

<p>Although the Readline library comes with a set of Emacs-like
keybindings installed by default, it is possible to use a different set
of keybindings.
Any user can customize programs that use Readline by putting
commands in an <em class="dfn">inputrc</em> file, conventionally in their home directory.
The name of this file is taken from the value of the
environment variable <code class="env">INPUTRC</code>.
If that variable is unset, the default is <samp class="file">~/.inputrc</samp>.
If that file does not exist or cannot be read, Readline looks for
<samp class="file">/etc/inputrc</samp>.
</p>
<p>When a program that uses the Readline library starts up, Readline reads
the init file and sets any variables and key bindings it contains.
</p>
<p>In addition, the <code class="code">C-x C-r</code> command re-reads this init file, thus
incorporating any changes that you might have made to it.
</p>

<ul class="mini-toc">
<li><a href="#Readline-Init-File-Syntax" accesskey="1">Readline Init File Syntax</a></li>
<li><a href="#Conditional-Init-Constructs" accesskey="2">Conditional Init Constructs</a></li>
<li><a href="#Sample-Init-File" accesskey="3">Sample Init File</a></li>
</ul>
<hr>
<div class="subsection-level-extent" id="Readline-Init-File-Syntax">
<div class="nav-panel">
<p>
Next: <a href="#Conditional-Init-Constructs" accesskey="n" rel="next">Conditional Init Constructs</a>, Up: <a href="#Readline-Init-File" accesskey="u" rel="up">Readline Init File</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Readline-Init-File-Syntax-1"><span>1.3.1 Readline Init File Syntax<a class="copiable-link" href="#Readline-Init-File-Syntax-1"> &para;</a></span></h4>

<p>There are only a few basic constructs allowed in the
Readline init file.
Blank lines are ignored.
Lines beginning with a &lsquo;<samp class="samp">#</samp>&rsquo; are comments.
Lines beginning with a &lsquo;<samp class="samp">$</samp>&rsquo; indicate conditional
constructs (see <a class="pxref" href="#Conditional-Init-Constructs">Conditional Init Constructs</a>).
Other lines denote variable settings and key bindings.
</p>
<dl class="table">
<dt>Variable Settings</dt>
<dd><p>You can modify the run-time behavior of Readline by
altering the values of variables in Readline
using the <code class="code">set</code> command within the init file.
The syntax is simple:
</p>
<div class="example">
<pre class="example-preformatted">set <var class="var">variable</var> <var class="var">value</var>
</pre></div>

<p>Here, for example, is how to
change from the default Emacs-like key binding to use
<code class="code">vi</code> line editing commands:
</p>
<div class="example">
<pre class="example-preformatted">set editing-mode vi
</pre></div>

<p>Variable names and values, where appropriate, are recognized without
regard to case.
Unrecognized variable names are ignored.
</p>
<p>Boolean variables (those that can be set to on or off) are set to on if
the value is null or empty, <var class="var">on</var> (case-insensitive), or 1.
Any other value results in the variable being set to off.
</p>

<p>A great deal of run-time behavior is changeable with the following
variables.
</p>
<a class="index-entry-id" id="index-variables_002c-readline"></a>
<dl class="table">
<dt><a id="index-active_002dregion_002dstart_002dcolor"></a><span><code class="code">active-region-start-color</code><a class="copiable-link" href="#index-active_002dregion_002dstart_002dcolor"> &para;</a></span></dt>
<dd><p>A string variable that controls the text color and background when displaying
the text in the active region (see the description of
<code class="code">enable-active-region</code> below).
This string must not take up any physical character positions on the display,
so it should consist only of terminal escape sequences.
It is output to the terminal before displaying the text in the active region.
This variable is reset to the default value whenever the terminal type changes.
The default value is the string that puts the terminal in standout mode,
as obtained from the terminal&rsquo;s terminfo description.
A sample value might be &lsquo;<samp class="samp">\e[01;33m</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-active_002dregion_002dend_002dcolor"></a><span><code class="code">active-region-end-color</code><a class="copiable-link" href="#index-active_002dregion_002dend_002dcolor"> &para;</a></span></dt>
<dd><p>A string variable that &ldquo;undoes&rdquo;
the effects of <code class="code">active-region-start-color</code>
and restores &ldquo;normal&rdquo;
terminal display appearance after displaying text in the active region.
This string must not take up any physical character positions on the display,
so it should consist only of terminal escape sequences.
It is output to the terminal after displaying the text in the active region.
This variable is reset to the default value whenever the terminal type changes.
The default value is the string that restores the terminal from standout mode,
as obtained from the terminal&rsquo;s terminfo description.
A sample value might be &lsquo;<samp class="samp">\e[0m</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-bell_002dstyle"></a><span><code class="code">bell-style</code><a class="copiable-link" href="#index-bell_002dstyle"> &para;</a></span></dt>
<dd><p>Controls what happens when Readline wants to ring the terminal bell.
If set to &lsquo;<samp class="samp">none</samp>&rsquo;, Readline never rings the bell.
If set to &lsquo;<samp class="samp">visible</samp>&rsquo;, Readline uses a visible bell if one is available.
If set to &lsquo;<samp class="samp">audible</samp>&rsquo; (the default), Readline attempts to ring
the terminal&rsquo;s bell.
</p>
</dd>
<dt><a id="index-bind_002dtty_002dspecial_002dchars"></a><span><code class="code">bind-tty-special-chars</code><a class="copiable-link" href="#index-bind_002dtty_002dspecial_002dchars"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo; (the default), Readline attempts to bind the control
characters that are
treated specially by the kernel&rsquo;s terminal driver to their
Readline equivalents.
These override the default Readline bindings described here.
Type &lsquo;<samp class="samp">stty -a</samp>&rsquo; at a Bash prompt to see your current terminal settings,
including the special control characters (usually <code class="code">cchars</code>).
</p>
</dd>
<dt><a id="index-blink_002dmatching_002dparen"></a><span><code class="code">blink-matching-paren</code><a class="copiable-link" href="#index-blink_002dmatching_002dparen"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline attempts to briefly move the cursor to an
opening parenthesis when a closing parenthesis is inserted.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-colored_002dcompletion_002dprefix"></a><span><code class="code">colored-completion-prefix</code><a class="copiable-link" href="#index-colored_002dcompletion_002dprefix"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, when listing completions, Readline displays the
common prefix of the set of possible completions using a different color.
The color definitions are taken from the value of the <code class="env">LS_COLORS</code>
environment variable.
If there is a color definition in <code class="env">LS_COLORS</code> for the custom suffix
&lsquo;<samp class="samp">readline-colored-completion-prefix</samp>&rsquo;, Readline uses this color for
the common prefix instead of its default.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-colored_002dstats"></a><span><code class="code">colored-stats</code><a class="copiable-link" href="#index-colored_002dstats"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline displays possible completions using different
colors to indicate their file type.
The color definitions are taken from the value of the <code class="env">LS_COLORS</code>
environment variable.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-comment_002dbegin"></a><span><code class="code">comment-begin</code><a class="copiable-link" href="#index-comment_002dbegin"> &para;</a></span></dt>
<dd><p>The string to insert at the beginning of the line by the
<code class="code">insert-comment</code> command.
The default value is <code class="code">&quot;#&quot;</code>.
</p>
</dd>
<dt><a id="index-completion_002ddisplay_002dwidth"></a><span><code class="code">completion-display-width</code><a class="copiable-link" href="#index-completion_002ddisplay_002dwidth"> &para;</a></span></dt>
<dd><p>The number of screen columns used to display possible matches
when performing completion.
The value is ignored if it is less than 0 or greater than the terminal
screen width.
A value of 0 causes matches to be displayed one per line.
The default value is -1.
</p>
</dd>
<dt><a id="index-completion_002dignore_002dcase"></a><span><code class="code">completion-ignore-case</code><a class="copiable-link" href="#index-completion_002dignore_002dcase"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline performs filename matching and completion
in a case-insensitive fashion.
The default value is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-completion_002dmap_002dcase"></a><span><code class="code">completion-map-case</code><a class="copiable-link" href="#index-completion_002dmap_002dcase"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, and <var class="var">completion-ignore-case</var> is enabled, Readline
treats hyphens (&lsquo;<samp class="samp">-</samp>&rsquo;) and underscores (&lsquo;<samp class="samp">_</samp>&rsquo;) as equivalent when
performing case-insensitive filename matching and completion.
The default value is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-completion_002dprefix_002ddisplay_002dlength"></a><span><code class="code">completion-prefix-display-length</code><a class="copiable-link" href="#index-completion_002dprefix_002ddisplay_002dlength"> &para;</a></span></dt>
<dd><p>The maximum
length in characters of the common prefix of a list of possible
completions that is displayed without modification.
When set to a value greater than zero, Readline
replaces common prefixes longer than this value
with an ellipsis when displaying possible completions.
If a completion begins with a period,
and Readline is completing filenames,
it uses three underscores instead of an ellipsis.
</p>
</dd>
<dt><a id="index-completion_002dquery_002ditems"></a><span><code class="code">completion-query-items</code><a class="copiable-link" href="#index-completion_002dquery_002ditems"> &para;</a></span></dt>
<dd><p>The number of possible completions that determines when the user is asked
whether the list of possibilities should be displayed.
If the number of possible completions is greater than
or equal to this value,
Readline asks whether or not the user wishes to view them;
otherwise, Readline simply lists the completions.
This variable must be set to an integer value greater than or equal to zero.
A zero value means Readline should never ask; negative
values are treated as zero.
The default limit is <code class="code">100</code>.
</p>
</dd>
<dt><a id="index-convert_002dmeta"></a><span><code class="code">convert-meta</code><a class="copiable-link" href="#index-convert_002dmeta"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline converts characters it reads
that have the eighth bit set to an <small class="sc">ASCII</small> key sequence by
clearing the eighth bit and prefixing an <kbd class="key">ESC</kbd> character,
converting them to a meta-prefixed key sequence.
The default value is &lsquo;<samp class="samp">on</samp>&rsquo;, but Readline sets it to &lsquo;<samp class="samp">off</samp>&rsquo;
if the locale contains
characters whose encodings may include bytes with the eighth bit set.
This variable is dependent on the <code class="code">LC_CTYPE</code> locale category, and
may change if the locale changes.
This variable also affects key bindings;
see the description of <code class="code">force-meta-prefix</code> below.
</p>
</dd>
<dt><a id="index-disable_002dcompletion"></a><span><code class="code">disable-completion</code><a class="copiable-link" href="#index-disable_002dcompletion"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">On</samp>&rsquo;, Readline inhibits word completion.
Completion characters are inserted into the line as if they
had been mapped to <code class="code">self-insert</code>.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-echo_002dcontrol_002dcharacters"></a><span><code class="code">echo-control-characters</code><a class="copiable-link" href="#index-echo_002dcontrol_002dcharacters"> &para;</a></span></dt>
<dd><p>When set to &lsquo;<samp class="samp">on</samp>&rsquo;, on operating systems that indicate they support it,
Readline echoes a character corresponding to a signal generated from the
keyboard.
The default is &lsquo;<samp class="samp">on</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-editing_002dmode"></a><span><code class="code">editing-mode</code><a class="copiable-link" href="#index-editing_002dmode"> &para;</a></span></dt>
<dd><p>The <code class="code">editing-mode</code> variable controls the default set of
key bindings.
By default, Readline starts up in emacs editing mode, where
the keystrokes are most similar to Emacs.
This variable can be set to either &lsquo;<samp class="samp">emacs</samp>&rsquo; or &lsquo;<samp class="samp">vi</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-emacs_002dmode_002dstring"></a><span><code class="code">emacs-mode-string</code><a class="copiable-link" href="#index-emacs_002dmode_002dstring"> &para;</a></span></dt>
<dd><p>If the <var class="var">show-mode-in-prompt</var> variable is enabled,
this string is displayed immediately before the last line of the primary
prompt when emacs editing mode is active.
The value is expanded like a
key binding, so the standard set of meta- and control- prefixes and
backslash escape sequences is available.
The &lsquo;<samp class="samp">\1</samp>&rsquo; and &lsquo;<samp class="samp">\2</samp>&rsquo; escapes begin and end sequences of
non-printing characters, which can be used to embed a terminal control
sequence into the mode string.
The default is &lsquo;<samp class="samp">@</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-enable_002dactive_002dregion-The"></a><span><code class="code">enable-active-region</code><a class="copiable-link" href="#index-enable_002dactive_002dregion-The"> &para;</a></span></dt>
<dd><p><em class="dfn">point</em> is the current cursor position, and <em class="dfn">mark</em> refers to a
saved cursor position (see <a class="pxref" href="#Commands-For-Moving">Commands For Moving</a>).
The text between the point and mark is referred to as the <em class="dfn">region</em>.
When this variable is set to &lsquo;<samp class="samp">On</samp>&rsquo;, Readline allows certain commands
to designate the region as <em class="dfn">active</em>.
When the region is active, Readline highlights the text in the region using
the value of the <code class="code">active-region-start-color</code>, which defaults to the
string that enables the terminal&rsquo;s standout mode. 
The active region shows the text inserted by bracketed-paste and any
matching text found by incremental and non-incremental history searches. 
The default is &lsquo;<samp class="samp">On</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-enable_002dbracketed_002dpaste"></a><span><code class="code">enable-bracketed-paste</code><a class="copiable-link" href="#index-enable_002dbracketed_002dpaste"> &para;</a></span></dt>
<dd><p>When set to &lsquo;<samp class="samp">On</samp>&rsquo;, Readline configures the terminal to insert each
paste into the editing buffer as a single string of characters, instead
of treating each character as if it had been read from the keyboard.
This is called putting the terminal into <em class="dfn">bracketed paste mode</em>;
it prevents Readline from executing any editing commands bound
to key sequences appearing in the pasted text.
The default is &lsquo;<samp class="samp">On</samp>&rsquo;. 
</p>
</dd>
<dt><a id="index-enable_002dkeypad"></a><span><code class="code">enable-keypad</code><a class="copiable-link" href="#index-enable_002dkeypad"> &para;</a></span></dt>
<dd><p>When set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline tries to enable the application
keypad when it is called.
Some systems need this to enable the arrow keys.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-enable_002dmeta_002dkey"></a><span><code class="code">enable-meta-key</code><a class="copiable-link" href="#index-enable_002dmeta_002dkey"> &para;</a></span></dt>
<dd><p>When set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline tries to enable any meta
modifier key the terminal claims to support when it is called.
On many terminals, the Meta key is used to send eight-bit characters;
this variable checks for the terminal capability that indicates the
terminal can enable and disable a mode that sets the eighth bit of a
character (0200) if the Meta key is held down when the character is
typed (a meta character).
The default is &lsquo;<samp class="samp">on</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-expand_002dtilde"></a><span><code class="code">expand-tilde</code><a class="copiable-link" href="#index-expand_002dtilde"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline attempts tilde expansion when it
attempts word completion.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-force_002dmeta_002dprefix"></a><span><code class="code">force-meta-prefix</code><a class="copiable-link" href="#index-force_002dmeta_002dprefix"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline modifies its behavior when binding key
sequences containing <kbd class="kbd">\M-</kbd> or <code class="code">Meta-</code>
(see <code class="code">Key Bindings</code> in <a class="ref" href="#Readline-Init-File-Syntax">Readline Init File Syntax</a>)
by converting a key sequence of the form
<kbd class="kbd">\M-</kbd><var class="var">C</var> or <code class="code">Meta-</code><var class="var">C</var> to the two-character sequence
<kbd class="kbd">ESC</kbd> <var class="var">C</var> (adding the meta prefix).
If <code class="code">force-meta-prefix</code> is set to &lsquo;<samp class="samp">off</samp>&rsquo; (the default),
Readline uses the value of the <code class="code">convert-meta</code> variable to determine
whether to perform this conversion:
if <code class="code">convert-meta</code> is &lsquo;<samp class="samp">on</samp>&rsquo;,
Readline performs the conversion described above;
if it is &lsquo;<samp class="samp">off</samp>&rsquo;, Readline converts <var class="var">C</var> to a meta character by
setting the eighth bit (0200).
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-history_002dpreserve_002dpoint"></a><span><code class="code">history-preserve-point</code><a class="copiable-link" href="#index-history_002dpreserve_002dpoint"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, the history code attempts to place the point (the
current cursor position) at the
same location on each history line retrieved with <code class="code">previous-history</code>
or <code class="code">next-history</code>.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-history_002dsize"></a><span><code class="code">history-size</code><a class="copiable-link" href="#index-history_002dsize"> &para;</a></span></dt>
<dd><p>Set the maximum number of history entries saved in the history list.
If set to zero, any existing history entries are deleted and no new entries
are saved.
If set to a value less than zero, the number of history entries is not
limited.
By default, the number of history entries is not limited.
If you try to set <var class="var">history-size</var> to a non-numeric value,
the maximum number of history entries will be set to 500.
</p>
</dd>
<dt><a id="index-horizontal_002dscroll_002dmode"></a><span><code class="code">horizontal-scroll-mode</code><a class="copiable-link" href="#index-horizontal_002dscroll_002dmode"> &para;</a></span></dt>
<dd><p>Setting this variable to &lsquo;<samp class="samp">on</samp>&rsquo; means that the text of the lines
being edited will scroll horizontally on a single screen line when
the lines are longer than the width of the screen, instead of wrapping
onto a new screen line.
This variable is automatically set to &lsquo;<samp class="samp">on</samp>&rsquo; for terminals of height 1.
By default, this variable is set to &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a class="index-entry-id" id="index-meta_002dflag"></a>
<a id="index-input_002dmeta"></a><span><code class="code">input-meta</code><a class="copiable-link" href="#index-input_002dmeta"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline enables eight-bit input (that is, it
does not clear the eighth bit in the characters it reads),
regardless of what the terminal claims it can support.
The default value is &lsquo;<samp class="samp">off</samp>&rsquo;, but Readline sets it to &lsquo;<samp class="samp">on</samp>&rsquo;
if the locale contains characters whose encodings may include bytes
with the eighth bit set.
This variable is dependent on the <code class="code">LC_CTYPE</code> locale category, and
its value may change if the locale changes.
The name <code class="code">meta-flag</code> is a synonym for <code class="code">input-meta</code>.
</p>
</dd>
<dt><a id="index-isearch_002dterminators"></a><span><code class="code">isearch-terminators</code><a class="copiable-link" href="#index-isearch_002dterminators"> &para;</a></span></dt>
<dd><p>The string of characters that should terminate an incremental search without
subsequently executing the character as a command (see <a class="pxref" href="#Searching">Searching for Commands in the History</a>).
If this variable has not been given a value, the characters <kbd class="key">ESC</kbd> and
<kbd class="kbd">C-j</kbd> terminate an incremental search.
</p>
</dd>
<dt><a id="index-keymap"></a><span><code class="code">keymap</code><a class="copiable-link" href="#index-keymap"> &para;</a></span></dt>
<dd><p>Sets Readline&rsquo;s idea of the current keymap for key binding commands.
Built-in <code class="code">keymap</code> names are
<code class="code">emacs</code>,
<code class="code">emacs-standard</code>,
<code class="code">emacs-meta</code>,
<code class="code">emacs-ctlx</code>,
<code class="code">vi</code>,
<code class="code">vi-move</code>,
<code class="code">vi-command</code>, and
<code class="code">vi-insert</code>.
<code class="code">vi</code> is equivalent to <code class="code">vi-command</code> (<code class="code">vi-move</code> is also a
synonym); <code class="code">emacs</code> is equivalent to <code class="code">emacs-standard</code>.
Applications may add additional names.
The default value is <code class="code">emacs</code>;
the value of the <code class="code">editing-mode</code> variable also affects the
default keymap.
</p>
</dd>
<dt><code class="code">keyseq-timeout</code></dt>
<dd><p>Specifies the duration Readline will wait for a character when
reading an ambiguous key sequence
(one that can form a complete key sequence using the input read so far,
or can take additional input to complete a longer key sequence).
If Readline doesn&rsquo;t receive any input within the timeout, it uses the
shorter but complete key sequence.
Readline uses this value to determine whether or not input is
available on the current input source (<code class="code">rl_instream</code> by default).
The value is specified in milliseconds, so a value of 1000 means that
Readline will wait one second for additional input.
If this variable is set to a value less than or equal to zero, or to a
non-numeric value, Readline waits until another key is pressed to
decide which key sequence to complete.
The default value is <code class="code">500</code>.
</p>
</dd>
<dt><code class="code">mark-directories</code></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, completed directory names have a slash appended.
The default is &lsquo;<samp class="samp">on</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-mark_002dmodified_002dlines"></a><span><code class="code">mark-modified-lines</code><a class="copiable-link" href="#index-mark_002dmodified_002dlines"> &para;</a></span></dt>
<dd><p>When this variable is set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline displays an
asterisk (&lsquo;<samp class="samp">*</samp>&rsquo;) at the start of history lines which have been modified.
This variable is &lsquo;<samp class="samp">off</samp>&rsquo; by default.
</p>
</dd>
<dt><a id="index-mark_002dsymlinked_002ddirectories"></a><span><code class="code">mark-symlinked-directories</code><a class="copiable-link" href="#index-mark_002dsymlinked_002ddirectories"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, completed names which are symbolic links to directories
have a slash appended, subject to the value of <code class="code">mark-directories</code>.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-match_002dhidden_002dfiles"></a><span><code class="code">match-hidden-files</code><a class="copiable-link" href="#index-match_002dhidden_002dfiles"> &para;</a></span></dt>
<dd><p>This variable, when set to &lsquo;<samp class="samp">on</samp>&rsquo;, forces Readline to match files whose
names begin with a &lsquo;<samp class="samp">.</samp>&rsquo; (hidden files) when performing filename
completion.
If set to &lsquo;<samp class="samp">off</samp>&rsquo;, the user must include the leading &lsquo;<samp class="samp">.</samp>&rsquo;
in the filename to be completed.
This variable is &lsquo;<samp class="samp">on</samp>&rsquo; by default.
</p>
</dd>
<dt><a id="index-menu_002dcomplete_002ddisplay_002dprefix"></a><span><code class="code">menu-complete-display-prefix</code><a class="copiable-link" href="#index-menu_002dcomplete_002ddisplay_002dprefix"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, menu completion displays the common prefix of the
list of possible completions (which may be empty) before cycling through
the list.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-output_002dmeta"></a><span><code class="code">output-meta</code><a class="copiable-link" href="#index-output_002dmeta"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline displays characters with the
eighth bit set directly rather than as a meta-prefixed escape
sequence.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;, but Readline sets it to &lsquo;<samp class="samp">on</samp>&rsquo;
if the locale contains characters whose encodings may include
bytes with the eighth bit set.
This variable is dependent on the <code class="code">LC_CTYPE</code> locale category, and
its value may change if the locale changes.
</p>
</dd>
<dt><a id="index-page_002dcompletions"></a><span><code class="code">page-completions</code><a class="copiable-link" href="#index-page_002dcompletions"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline uses an internal pager resembling
<i class="i">more</i>(1)
to display a screenful of possible completions at a time.
This variable is &lsquo;<samp class="samp">on</samp>&rsquo; by default.
</p>
</dd>
<dt><code class="code">prefer-visible-bell</code></dt>
<dd><p>See <code class="code">bell-style</code>.
</p>
</dd>
<dt><code class="code">print-completions-horizontally</code></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline displays completions with matches
sorted horizontally in alphabetical order, rather than down the screen.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-revert_002dall_002dat_002dnewline"></a><span><code class="code">revert-all-at-newline</code><a class="copiable-link" href="#index-revert_002dall_002dat_002dnewline"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline will undo all changes to history lines
before returning when executing <code class="code">accept-line</code>.
By default,
history lines may be modified and retain individual undo lists across
calls to <code class="code">readline()</code>.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-search_002dignore_002dcase"></a><span><code class="code">search-ignore-case</code><a class="copiable-link" href="#index-search_002dignore_002dcase"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, Readline performs incremental and non-incremental
history list searches in a case-insensitive fashion.
The default value is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-show_002dall_002dif_002dambiguous"></a><span><code class="code">show-all-if-ambiguous</code><a class="copiable-link" href="#index-show_002dall_002dif_002dambiguous"> &para;</a></span></dt>
<dd><p>This alters the default behavior of the completion functions.
If set to &lsquo;<samp class="samp">on</samp>&rsquo;, 
words which have more than one possible completion cause the
matches to be listed immediately instead of ringing the bell.
The default value is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-show_002dall_002dif_002dunmodified"></a><span><code class="code">show-all-if-unmodified</code><a class="copiable-link" href="#index-show_002dall_002dif_002dunmodified"> &para;</a></span></dt>
<dd><p>This alters the default behavior of the completion functions in
a fashion similar to <var class="var">show-all-if-ambiguous</var>.
If set to &lsquo;<samp class="samp">on</samp>&rsquo;, 
words which have more than one possible completion without any
possible partial completion (the possible completions don&rsquo;t share
a common prefix) cause the matches to be listed immediately instead
of ringing the bell.
The default value is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-show_002dmode_002din_002dprompt"></a><span><code class="code">show-mode-in-prompt</code><a class="copiable-link" href="#index-show_002dmode_002din_002dprompt"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, add a string to the beginning of the prompt
indicating the editing mode: emacs, vi command, or vi insertion.
The mode strings are user-settable (e.g., <var class="var">emacs-mode-string</var>).
The default value is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-skip_002dcompleted_002dtext"></a><span><code class="code">skip-completed-text</code><a class="copiable-link" href="#index-skip_002dcompleted_002dtext"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, this alters the default completion behavior when
inserting a single match into the line.
It&rsquo;s only active when performing completion in the middle of a word.
If enabled, Readline does not insert characters from the completion
that match characters after point in the word being completed,
so portions of the word following the cursor are not duplicated.
For instance, if this is enabled, attempting completion when the cursor
is after the first &lsquo;<samp class="samp">e</samp>&rsquo; in &lsquo;<samp class="samp">Makefile</samp>&rsquo; will result in
&lsquo;<samp class="samp">Makefile</samp>&rsquo; rather than &lsquo;<samp class="samp">Makefilefile</samp>&rsquo;,
assuming there is a single possible completion.
The default value is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-vi_002dcmd_002dmode_002dstring"></a><span><code class="code">vi-cmd-mode-string</code><a class="copiable-link" href="#index-vi_002dcmd_002dmode_002dstring"> &para;</a></span></dt>
<dd><p>If the <var class="var">show-mode-in-prompt</var> variable is enabled,
this string is displayed immediately before the last line of the primary
prompt when vi editing mode is active and in command mode.
The value is expanded like a key binding, so the standard set of
meta- and control- prefixes and backslash escape sequences is available.
The &lsquo;<samp class="samp">\1</samp>&rsquo; and &lsquo;<samp class="samp">\2</samp>&rsquo; escapes begin and end sequences of
non-printing characters, which can be used to embed a terminal control
sequence into the mode string.
The default is &lsquo;<samp class="samp">(cmd)</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-vi_002dins_002dmode_002dstring"></a><span><code class="code">vi-ins-mode-string</code><a class="copiable-link" href="#index-vi_002dins_002dmode_002dstring"> &para;</a></span></dt>
<dd><p>If the <var class="var">show-mode-in-prompt</var> variable is enabled,
this string is displayed immediately before the last line of the primary
prompt when vi editing mode is active and in insertion mode.
The value is expanded like a key binding, so the standard set of
meta- and control- prefixes and backslash escape sequences is available.
The &lsquo;<samp class="samp">\1</samp>&rsquo; and &lsquo;<samp class="samp">\2</samp>&rsquo; escapes begin and end sequences of
non-printing characters, which can be used to embed a terminal control
sequence into the mode string.
The default is &lsquo;<samp class="samp">(ins)</samp>&rsquo;.
</p>
</dd>
<dt><a id="index-visible_002dstats"></a><span><code class="code">visible-stats</code><a class="copiable-link" href="#index-visible_002dstats"> &para;</a></span></dt>
<dd><p>If set to &lsquo;<samp class="samp">on</samp>&rsquo;, a character denoting a file&rsquo;s type
is appended to the filename when listing possible
completions.
The default is &lsquo;<samp class="samp">off</samp>&rsquo;.
</p>
</dd>
</dl>

</dd>
<dt>Key Bindings</dt>
<dd><p>The syntax for controlling key bindings in the init file is simple.
First you need to find the name of the command that you
want to change.
The following sections contain tables of the command
name, the default keybinding, if any, and a short description of what
the command does.
</p>
<p>Once you know the name of the command, simply place on a line
in the init file the name of the key
you wish to bind the command to, a colon, and then the name of the
command.
There can be no space between the key name and the colon &ndash; that will be
interpreted as part of the key name.
The name of the key can be expressed in different ways, depending on
what you find most comfortable.
</p>
<p>In addition to command names, Readline allows keys to be bound
to a string that is inserted when the key is pressed (a <var class="var">macro</var>).
The difference between a macro and a command is that a macro is
enclosed in single or double quotes.
</p>

<dl class="table">
<dt><var class="var">keyname</var>:&nbsp;<var class="var">function-name</var>&nbsp;or&nbsp;<var class="var">macro</var><!-- /@w --></dt>
<dd><p><var class="var">keyname</var> is the name of a key spelled out in English.
For example:
</p><div class="example">
<pre class="example-preformatted">Control-u: universal-argument
Meta-Rubout: backward-kill-word
Control-o: &quot;&gt; output&quot;
</pre></div>

<p>In the example above, <kbd class="kbd">C-u</kbd> is bound to the function
<code class="code">universal-argument</code>,
<kbd class="kbd">M-DEL</kbd> is bound to the function <code class="code">backward-kill-word</code>, and
<kbd class="kbd">C-o</kbd> is bound to run the macro
expressed on the right hand side (that is, to insert the text
&lsquo;<samp class="samp">&gt; output</samp>&rsquo; into the line).
</p>
<p>This key binding syntax recognizes a number of symbolic character names:
<var class="var">DEL</var>,
<var class="var">ESC</var>,
<var class="var">ESCAPE</var>,
<var class="var">LFD</var>,
<var class="var">NEWLINE</var>,
<var class="var">RET</var>,
<var class="var">RETURN</var>,
<var class="var">RUBOUT</var>
(a destructive backspace),
<var class="var">SPACE</var>,
<var class="var">SPC</var>,
and
<var class="var">TAB</var>.
</p>
</dd>
<dt>&quot;<var class="var">keyseq</var>&quot;:&nbsp;<var class="var">function-name</var>&nbsp;or&nbsp;<var class="var">macro</var><!-- /@w --></dt>
<dd><p><var class="var">keyseq</var> differs from <var class="var">keyname</var> above in that strings
denoting an entire key sequence can be specified, by placing
the key sequence in double quotes.
Some <small class="sc">GNU</small> Emacs style key escapes can be used,
as in the following example, but none of the
special character names are recognized.
</p>
<div class="example">
<pre class="example-preformatted">&quot;\C-u&quot;: universal-argument
&quot;\C-x\C-r&quot;: re-read-init-file
&quot;\e[11~&quot;: &quot;Function Key 1&quot;
</pre></div>

<p>In the above example, <kbd class="kbd">C-u</kbd> is again bound to the function
<code class="code">universal-argument</code> (just as it was in the first example),
&lsquo;<samp class="samp"><kbd class="kbd">C-x</kbd> <kbd class="kbd">C-r</kbd></samp>&rsquo; is bound to the function <code class="code">re-read-init-file</code>,
and &lsquo;<samp class="samp"><kbd class="key">ESC</kbd> <kbd class="key">[</kbd> <kbd class="key">1</kbd> <kbd class="key">1</kbd> <kbd class="key">~</kbd></samp>&rsquo; is bound to insert
the text &lsquo;<samp class="samp">Function Key 1</samp>&rsquo;.
</p>
</dd>
</dl>

<p>The following <small class="sc">GNU</small> Emacs style escape sequences are available when
specifying key sequences:
</p>
<dl class="table">
<dt><code class="code"><kbd class="kbd">\C-</kbd></code></dt>
<dd><p>A control prefix.
</p></dd>
<dt><code class="code"><kbd class="kbd">\M-</kbd></code></dt>
<dd><p>Adding the meta prefix or converting the following character to a meta
character, as described above under <code class="code">force-meta-prefix</code>
(see <code class="code">Variable Settings</code> in <a class="ref" href="#Readline-Init-File-Syntax">Readline Init File Syntax</a>).
</p></dd>
<dt><code class="code"><kbd class="kbd">\e</kbd></code></dt>
<dd><p>An escape character.
</p></dd>
<dt><code class="code"><kbd class="kbd">\\</kbd></code></dt>
<dd><p>Backslash.
</p></dd>
<dt><code class="code"><kbd class="kbd">\&quot;</kbd></code></dt>
<dd><p><kbd class="key">&quot;</kbd>, a double quotation mark.
</p></dd>
<dt><code class="code"><kbd class="kbd">\'</kbd></code></dt>
<dd><p><kbd class="key">'</kbd>, a single quote or apostrophe.
</p></dd>
</dl>

<p>In addition to the <small class="sc">GNU</small> Emacs style escape sequences, a second
set of backslash escapes is available:
</p>
<dl class="table">
<dt><code class="code">\a</code></dt>
<dd><p>alert (bell)
</p></dd>
<dt><code class="code">\b</code></dt>
<dd><p>backspace
</p></dd>
<dt><code class="code">\d</code></dt>
<dd><p>delete
</p></dd>
<dt><code class="code">\f</code></dt>
<dd><p>form feed
</p></dd>
<dt><code class="code">\n</code></dt>
<dd><p>newline
</p></dd>
<dt><code class="code">\r</code></dt>
<dd><p>carriage return
</p></dd>
<dt><code class="code">\t</code></dt>
<dd><p>horizontal tab
</p></dd>
<dt><code class="code">\v</code></dt>
<dd><p>vertical tab
</p></dd>
<dt><code class="code">\<var class="var">nnn</var></code></dt>
<dd><p>The eight-bit character whose value is the octal value <var class="var">nnn</var>
(one to three digits).
</p></dd>
<dt><code class="code">\x<var class="var">HH</var></code></dt>
<dd><p>The eight-bit character whose value is the hexadecimal value <var class="var">HH</var>
(one or two hex digits).
</p></dd>
</dl>

<p>When entering the text of a macro, single or double quotes must
be used to indicate a macro definition.
Unquoted text is assumed to be a function name.
The backslash escapes described above are expanded
in the macro body.
Backslash will quote any other character in the macro text,
including &lsquo;<samp class="samp">&quot;</samp>&rsquo; and &lsquo;<samp class="samp">'</samp>&rsquo;.
For example, the following binding will make &lsquo;<samp class="samp"><kbd class="kbd">C-x</kbd> \</samp>&rsquo;
insert a single &lsquo;<samp class="samp">\</samp>&rsquo; into the line:
</p><div class="example">
<pre class="example-preformatted">&quot;\C-x\\&quot;: &quot;\\&quot;
</pre></div>

</dd>
</dl>

<hr>
</div>
<div class="subsection-level-extent" id="Conditional-Init-Constructs">
<div class="nav-panel">
<p>
Next: <a href="#Sample-Init-File" accesskey="n" rel="next">Sample Init File</a>, Previous: <a href="#Readline-Init-File-Syntax" accesskey="p" rel="prev">Readline Init File Syntax</a>, Up: <a href="#Readline-Init-File" accesskey="u" rel="up">Readline Init File</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Conditional-Init-Constructs-1"><span>1.3.2 Conditional Init Constructs<a class="copiable-link" href="#Conditional-Init-Constructs-1"> &para;</a></span></h4>

<p>Readline implements a facility similar in spirit to the conditional
compilation features of the C preprocessor which allows key
bindings and variable settings to be performed as the result
of tests.
There are four parser directives available.
</p>
<dl class="table">
<dt><code class="code">$if</code></dt>
<dd><p>The <code class="code">$if</code> construct allows bindings to be made based on the
editing mode, the terminal being used, or the application using
Readline.
The text of the test, after any comparison operator,
extends to the end of the line;
unless otherwise noted, no characters are required to isolate it.
</p>
<dl class="table">
<dt><code class="code">mode</code></dt>
<dd><p>The <code class="code">mode=</code> form of the <code class="code">$if</code> directive is used to test
whether Readline is in <code class="code">emacs</code> or <code class="code">vi</code> mode.
This may be used in conjunction
with the &lsquo;<samp class="samp">set keymap</samp>&rsquo; command, for instance, to set bindings in
the <code class="code">emacs-standard</code> and <code class="code">emacs-ctlx</code> keymaps only if
Readline is starting out in <code class="code">emacs</code> mode.
</p>
</dd>
<dt><code class="code">term</code></dt>
<dd><p>The <code class="code">term=</code> form may be used to include terminal-specific
key bindings, perhaps to bind the key sequences output by the
terminal&rsquo;s function keys.
The word on the right side of the
&lsquo;<samp class="samp">=</samp>&rsquo;
is tested against both the full name of the terminal and the portion
of the terminal name before the first &lsquo;<samp class="samp">-</samp>&rsquo;.
This allows <code class="code">xterm</code> to match both <code class="code">xterm</code> and
<code class="code">xterm-256color</code>, for instance.
</p>
</dd>
<dt><code class="code">version</code></dt>
<dd><p>The <code class="code">version</code> test may be used to perform comparisons against
specific Readline versions.
The <code class="code">version</code> expands to the current Readline version.
The set of comparison operators includes
&lsquo;<samp class="samp">=</samp>&rsquo; (and &lsquo;<samp class="samp">==</samp>&rsquo;), &lsquo;<samp class="samp">!=</samp>&rsquo;, &lsquo;<samp class="samp">&lt;=</samp>&rsquo;, &lsquo;<samp class="samp">&gt;=</samp>&rsquo;, &lsquo;<samp class="samp">&lt;</samp>&rsquo;,
and &lsquo;<samp class="samp">&gt;</samp>&rsquo;.
The version number supplied on the right side of the operator consists
of a major version number, an optional decimal point, and an optional
minor version (e.g., &lsquo;<samp class="samp">7.1</samp>&rsquo;).
If the minor version is omitted, it
defaults to &lsquo;<samp class="samp">0</samp>&rsquo;.
The operator may be separated from the string <code class="code">version</code> and
from the version number argument by whitespace.
The following example sets a variable if the Readline version being used
is 7.0 or newer:
</p><div class="example">
<pre class="example-preformatted">$if version &gt;= 7.0
set show-mode-in-prompt on
$endif
</pre></div>

</dd>
<dt><code class="code">application</code></dt>
<dd><p>The <var class="var">application</var> construct is used to include
application-specific settings.
Each program using the Readline
library sets the <var class="var">application name</var>, and you can test for
a particular value. 
This could be used to bind key sequences to functions useful for
a specific program.
For instance, the following command adds a
key sequence that quotes the current or previous word in Bash:
</p><div class="example">
<pre class="example-preformatted">$if Bash
# Quote the current or previous word
&quot;\C-xq&quot;: &quot;\eb\&quot;\ef\&quot;&quot;
$endif
</pre></div>

</dd>
<dt><code class="code">variable</code></dt>
<dd><p>The <var class="var">variable</var> construct provides simple equality tests for Readline
variables and values.
The permitted comparison operators are &lsquo;<samp class="samp">=</samp>&rsquo;, &lsquo;<samp class="samp">==</samp>&rsquo;, and &lsquo;<samp class="samp">!=</samp>&rsquo;.
The variable name must be separated from the comparison operator by
whitespace; the operator may be separated from the value on the right hand
side by whitespace.
String and boolean variables may be tested.
Boolean variables must be
tested against the values <var class="var">on</var> and <var class="var">off</var>.
The following example is equivalent to the <code class="code">mode=emacs</code> test described
above:
</p><div class="example">
<pre class="example-preformatted">$if editing-mode == emacs
set show-mode-in-prompt on
$endif
</pre></div>
</dd>
</dl>

</dd>
<dt><code class="code">$else</code></dt>
<dd><p>Commands in this branch of the <code class="code">$if</code> directive are executed if
the test fails.
</p>
</dd>
<dt><code class="code">$endif</code></dt>
<dd><p>This command, as seen in the previous example, terminates an
<code class="code">$if</code> command.
</p>
</dd>
<dt><code class="code">$include</code></dt>
<dd><p>This directive takes a single filename as an argument and reads commands
and key bindings from that file.
For example, the following directive reads from <samp class="file">/etc/inputrc</samp>:
</p><div class="example">
<pre class="example-preformatted">$include /etc/inputrc
</pre></div>
</dd>
</dl>

<hr>
</div>
<div class="subsection-level-extent" id="Sample-Init-File">
<div class="nav-panel">
<p>
Previous: <a href="#Conditional-Init-Constructs" accesskey="p" rel="prev">Conditional Init Constructs</a>, Up: <a href="#Readline-Init-File" accesskey="u" rel="up">Readline Init File</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Sample-Init-File-1"><span>1.3.3 Sample Init File<a class="copiable-link" href="#Sample-Init-File-1"> &para;</a></span></h4>

<p>Here is an example of an <var class="var">inputrc</var> file.  This illustrates key
binding, variable assignment, and conditional syntax.
</p>
<div class="example">
<pre class="example-preformatted"># This file controls the behavior of line input editing for
# programs that use the GNU Readline library.  Existing
# programs include FTP, Bash, and GDB.
#
# You can re-read the inputrc file with C-x C-r.
# Lines beginning with '#' are comments.
#
# First, include any system-wide bindings and variable
# assignments from /etc/Inputrc
$include /etc/Inputrc

#
# Set various bindings for emacs mode.

set editing-mode emacs 

$if mode=emacs

Meta-Control-h:	backward-kill-word	Text after the function name is ignored

#
# Arrow keys in keypad mode
#
#&quot;\M-OD&quot;:        backward-char
#&quot;\M-OC&quot;:        forward-char
#&quot;\M-OA&quot;:        previous-history
#&quot;\M-OB&quot;:        next-history
#
# Arrow keys in ANSI mode
#
&quot;\M-[D&quot;:        backward-char
&quot;\M-[C&quot;:        forward-char
&quot;\M-[A&quot;:        previous-history
&quot;\M-[B&quot;:        next-history
#
# Arrow keys in 8 bit keypad mode
#
#&quot;\M-\C-OD&quot;:       backward-char
#&quot;\M-\C-OC&quot;:       forward-char
#&quot;\M-\C-OA&quot;:       previous-history
#&quot;\M-\C-OB&quot;:       next-history
#
# Arrow keys in 8 bit ANSI mode
#
#&quot;\M-\C-[D&quot;:       backward-char
#&quot;\M-\C-[C&quot;:       forward-char
#&quot;\M-\C-[A&quot;:       previous-history
#&quot;\M-\C-[B&quot;:       next-history

C-q: quoted-insert

$endif

# An old-style binding.  This happens to be the default.
TAB: complete

# Macros that are convenient for shell interaction
$if Bash
# edit the path
&quot;\C-xp&quot;: &quot;PATH=${PATH}\e\C-e\C-a\ef\C-f&quot;
# prepare to type a quoted word --
# insert open and close double quotes
# and move to just after the open quote
&quot;\C-x\&quot;&quot;: &quot;\&quot;\&quot;\C-b&quot;
# insert a backslash (testing backslash escapes
# in sequences and macros)
&quot;\C-x\\&quot;: &quot;\\&quot;
# Quote the current or previous word
&quot;\C-xq&quot;: &quot;\eb\&quot;\ef\&quot;&quot;
# Add a binding to refresh the line, which is unbound
&quot;\C-xr&quot;: redraw-current-line
# Edit variable on current line.
&quot;\M-\C-v&quot;: &quot;\C-a\C-k$\C-y\M-\C-e\C-a\C-y=&quot;
$endif

# use a visible bell if one is available
set bell-style visible

# don't strip characters to 7 bits when reading
set input-meta on

# allow iso-latin1 characters to be inserted rather
# than converted to prefix-meta sequences
set convert-meta off

# display characters with the eighth bit set directly
# rather than as meta-prefixed characters
set output-meta on

# if there are 150 or more possible completions for a word,
# ask whether or not the user wants to see all of them
set completion-query-items 150

# For FTP
$if Ftp
&quot;\C-xg&quot;: &quot;get \M-?&quot;
&quot;\C-xt&quot;: &quot;put \M-?&quot;
&quot;\M-.&quot;: yank-last-arg
$endif
</pre></div>

<hr>
</div>
</div>
<div class="section-level-extent" id="Bindable-Readline-Commands">
<div class="nav-panel">
<p>
Next: <a href="#Readline-vi-Mode" accesskey="n" rel="next">Readline vi Mode</a>, Previous: <a href="#Readline-Init-File" accesskey="p" rel="prev">Readline Init File</a>, Up: <a href="#Command-Line-Editing" accesskey="u" rel="up">Command Line Editing</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h3 class="section" id="Bindable-Readline-Commands-1"><span>1.4 Bindable Readline Commands<a class="copiable-link" href="#Bindable-Readline-Commands-1"> &para;</a></span></h3>


<p>This section describes Readline commands that may be bound to key
sequences.
Command names without an accompanying key sequence are unbound by default.
</p>
<p>In the following descriptions, <em class="dfn">point</em> refers to the current cursor
position, and <em class="dfn">mark</em> refers to a cursor position saved by the
<code class="code">set-mark</code> command.
The text between the point and mark is referred to as the <em class="dfn">region</em>.
Readline
has the concept of an <em class="emph">active region</em>:
when the region is active,
Readline redisplay highlights the region using the
value of the
<code class="code">active-region-start-color</code>
variable.
The <code class="code">enable-active-region</code> variable turns this on and off.
Several commands set the region to active; those are noted below.
</p>
<ul class="mini-toc">
<li><a href="#Commands-For-Moving" accesskey="1">Commands For Moving</a></li>
<li><a href="#Commands-For-History" accesskey="2">Commands For Manipulating The History</a></li>
<li><a href="#Commands-For-Text" accesskey="3">Commands For Changing Text</a></li>
<li><a href="#Commands-For-Killing" accesskey="4">Killing And Yanking</a></li>
<li><a href="#Numeric-Arguments" accesskey="5">Specifying Numeric Arguments</a></li>
<li><a href="#Commands-For-Completion" accesskey="6">Letting Readline Type For You</a></li>
<li><a href="#Keyboard-Macros" accesskey="7">Keyboard Macros</a></li>
<li><a href="#Miscellaneous-Commands" accesskey="8">Some Miscellaneous Commands</a></li>
</ul>
<hr>
<div class="subsection-level-extent" id="Commands-For-Moving">
<div class="nav-panel">
<p>
Next: <a href="#Commands-For-History" accesskey="n" rel="next">Commands For Manipulating The History</a>, Up: <a href="#Bindable-Readline-Commands" accesskey="u" rel="up">Bindable Readline Commands</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Commands-For-Moving-1"><span>1.4.1 Commands For Moving<a class="copiable-link" href="#Commands-For-Moving-1"> &para;</a></span></h4>
<dl class="ftable">
<dt><a id="index-beginning_002dof_002dline-_0028C_002da_0029"></a><span><code class="code">beginning-of-line (C-a)</code><a class="copiable-link" href="#index-beginning_002dof_002dline-_0028C_002da_0029"> &para;</a></span></dt>
<dd><p>Move to the start of the current line.
This may also be bound to the Home key on some keyboards.
</p>
</dd>
<dt><a id="index-end_002dof_002dline-_0028C_002de_0029"></a><span><code class="code">end-of-line (C-e)</code><a class="copiable-link" href="#index-end_002dof_002dline-_0028C_002de_0029"> &para;</a></span></dt>
<dd><p>Move to the end of the line.
This may also be bound to the End key on some keyboards.
</p>
</dd>
<dt><a id="index-forward_002dchar-_0028C_002df_0029"></a><span><code class="code">forward-char (C-f)</code><a class="copiable-link" href="#index-forward_002dchar-_0028C_002df_0029"> &para;</a></span></dt>
<dd><p>Move forward a character.
This may also be bound to the right arrow key on some keyboards.
</p>
</dd>
<dt><a id="index-backward_002dchar-_0028C_002db_0029"></a><span><code class="code">backward-char (C-b)</code><a class="copiable-link" href="#index-backward_002dchar-_0028C_002db_0029"> &para;</a></span></dt>
<dd><p>Move back a character.
This may also be bound to the left arrow key on some keyboards.
</p>
</dd>
<dt><a id="index-forward_002dword-_0028M_002df_0029"></a><span><code class="code">forward-word (M-f)</code><a class="copiable-link" href="#index-forward_002dword-_0028M_002df_0029"> &para;</a></span></dt>
<dd><p>Move forward to the end of the next word.
Words are composed of letters and digits.
</p>
</dd>
<dt><a id="index-backward_002dword-_0028M_002db_0029"></a><span><code class="code">backward-word (M-b)</code><a class="copiable-link" href="#index-backward_002dword-_0028M_002db_0029"> &para;</a></span></dt>
<dd><p>Move back to the start of the current or previous word.
Words are composed of letters and digits.
</p>

</dd>
<dt><a id="index-previous_002dscreen_002dline-_0028_0029"></a><span><code class="code">previous-screen-line ()</code><a class="copiable-link" href="#index-previous_002dscreen_002dline-_0028_0029"> &para;</a></span></dt>
<dd><p>Attempt to move point to the same physical screen column on the previous
physical screen line.
This will not have the desired effect if the current
Readline line does not take up more than one physical line or if point is not
greater than the length of the prompt plus the screen width.
</p>
</dd>
<dt><a id="index-next_002dscreen_002dline-_0028_0029"></a><span><code class="code">next-screen-line ()</code><a class="copiable-link" href="#index-next_002dscreen_002dline-_0028_0029"> &para;</a></span></dt>
<dd><p>Attempt to move point to the same physical screen column on the next
physical screen line.
This will not have the desired effect if the current
Readline line does not take up more than one physical line or if the length
of the current Readline line is not greater than the length of the prompt
plus the screen width.
</p>
</dd>
<dt><a id="index-clear_002ddisplay-_0028M_002dC_002dl_0029"></a><span><code class="code">clear-display (M-C-l)</code><a class="copiable-link" href="#index-clear_002ddisplay-_0028M_002dC_002dl_0029"> &para;</a></span></dt>
<dd><p>Clear the screen and, if possible, the terminal&rsquo;s scrollback buffer,
then redraw the current line,
leaving the current line at the top of the screen.
</p>
</dd>
<dt><a id="index-clear_002dscreen-_0028C_002dl_0029"></a><span><code class="code">clear-screen (C-l)</code><a class="copiable-link" href="#index-clear_002dscreen-_0028C_002dl_0029"> &para;</a></span></dt>
<dd><p>Clear the screen,
then redraw the current line,
leaving the current line at the top of the screen.
If given a numeric argument, this refreshes the current line
without clearing the screen.
</p>
</dd>
<dt><a id="index-redraw_002dcurrent_002dline-_0028_0029"></a><span><code class="code">redraw-current-line ()</code><a class="copiable-link" href="#index-redraw_002dcurrent_002dline-_0028_0029"> &para;</a></span></dt>
<dd><p>Refresh the current line.  By default, this is unbound.
</p>
</dd>
</dl>

<hr>
</div>
<div class="subsection-level-extent" id="Commands-For-History">
<div class="nav-panel">
<p>
Next: <a href="#Commands-For-Text" accesskey="n" rel="next">Commands For Changing Text</a>, Previous: <a href="#Commands-For-Moving" accesskey="p" rel="prev">Commands For Moving</a>, Up: <a href="#Bindable-Readline-Commands" accesskey="u" rel="up">Bindable Readline Commands</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Commands-For-Manipulating-The-History"><span>1.4.2 Commands For Manipulating The History<a class="copiable-link" href="#Commands-For-Manipulating-The-History"> &para;</a></span></h4>

<dl class="ftable">
<dt><a id="index-accept_002dline-_0028Newline-or-Return_0029"></a><span><code class="code">accept-line (Newline or Return)</code><a class="copiable-link" href="#index-accept_002dline-_0028Newline-or-Return_0029"> &para;</a></span></dt>
<dd><p>Accept the line regardless of where the cursor is.
If this line is non-empty, you can add it to the history list using
<code class="code">add_history()</code>.
If this line is a modified history line, then restore the history line
to its original state.
</p>
</dd>
<dt><a id="index-previous_002dhistory-_0028C_002dp_0029"></a><span><code class="code">previous-history (C-p)</code><a class="copiable-link" href="#index-previous_002dhistory-_0028C_002dp_0029"> &para;</a></span></dt>
<dd><p>Move &lsquo;back&rsquo; through the history list, fetching the previous command.
This may also be bound to the up arrow key on some keyboards.
</p>
</dd>
<dt><a id="index-next_002dhistory-_0028C_002dn_0029"></a><span><code class="code">next-history (C-n)</code><a class="copiable-link" href="#index-next_002dhistory-_0028C_002dn_0029"> &para;</a></span></dt>
<dd><p>Move &lsquo;forward&rsquo; through the history list, fetching the next command.
This may also be bound to the down arrow key on some keyboards.
</p>
</dd>
<dt><a id="index-beginning_002dof_002dhistory-_0028M_002d_003c_0029"></a><span><code class="code">beginning-of-history (M-&lt;)</code><a class="copiable-link" href="#index-beginning_002dof_002dhistory-_0028M_002d_003c_0029"> &para;</a></span></dt>
<dd><p>Move to the first line in the history.
</p>
</dd>
<dt><a id="index-end_002dof_002dhistory-_0028M_002d_003e_0029"></a><span><code class="code">end-of-history (M-&gt;)</code><a class="copiable-link" href="#index-end_002dof_002dhistory-_0028M_002d_003e_0029"> &para;</a></span></dt>
<dd><p>Move to the end of the input history, i.e., the line currently
being entered.
</p>
</dd>
<dt><a id="index-reverse_002dsearch_002dhistory-_0028C_002dr_0029"></a><span><code class="code">reverse-search-history (C-r)</code><a class="copiable-link" href="#index-reverse_002dsearch_002dhistory-_0028C_002dr_0029"> &para;</a></span></dt>
<dd><p>Search backward starting at the current line and moving &lsquo;up&rsquo; through
the history as necessary.
This is an incremental search.
This command sets the region to the matched text and activates the region.
</p>
</dd>
<dt><a id="index-forward_002dsearch_002dhistory-_0028C_002ds_0029"></a><span><code class="code">forward-search-history (C-s)</code><a class="copiable-link" href="#index-forward_002dsearch_002dhistory-_0028C_002ds_0029"> &para;</a></span></dt>
<dd><p>Search forward starting at the current line and moving &lsquo;down&rsquo; through
the history as necessary.
This is an incremental search.
This command sets the region to the matched text and activates the region.
</p>
</dd>
<dt><a id="index-non_002dincremental_002dreverse_002dsearch_002dhistory-_0028M_002dp_0029"></a><span><code class="code">non-incremental-reverse-search-history (M-p)</code><a class="copiable-link" href="#index-non_002dincremental_002dreverse_002dsearch_002dhistory-_0028M_002dp_0029"> &para;</a></span></dt>
<dd><p>Search backward starting at the current line and moving &lsquo;up&rsquo;
through the history as necessary using a non-incremental search
for a string supplied by the user.
The search string may match anywhere in a history line.
</p>
</dd>
<dt><a id="index-non_002dincremental_002dforward_002dsearch_002dhistory-_0028M_002dn_0029"></a><span><code class="code">non-incremental-forward-search-history (M-n)</code><a class="copiable-link" href="#index-non_002dincremental_002dforward_002dsearch_002dhistory-_0028M_002dn_0029"> &para;</a></span></dt>
<dd><p>Search forward starting at the current line and moving &lsquo;down&rsquo;
through the history as necessary using a non-incremental search
for a string supplied by the user.
The search string may match anywhere in a history line.
</p>
</dd>
<dt><a id="index-history_002dsearch_002dbackward-_0028_0029"></a><span><code class="code">history-search-backward ()</code><a class="copiable-link" href="#index-history_002dsearch_002dbackward-_0028_0029"> &para;</a></span></dt>
<dd><p>Search backward through the history for the string of characters
between the start of the current line and the point.
The search string must match at the beginning of a history line.
This is a non-incremental search.
By default, this command is unbound, but may be bound to the Page Down
key on some keyboards.
</p>
</dd>
<dt><a id="index-history_002dsearch_002dforward-_0028_0029"></a><span><code class="code">history-search-forward ()</code><a class="copiable-link" href="#index-history_002dsearch_002dforward-_0028_0029"> &para;</a></span></dt>
<dd><p>Search forward through the history for the string of characters
between the start of the current line and the point.
The search string must match at the beginning of a history line.
This is a non-incremental search.
By default, this command is unbound, but may be bound to the Page Up
key on some keyboards.
</p>
</dd>
<dt><a id="index-history_002dsubstring_002dsearch_002dbackward-_0028_0029"></a><span><code class="code">history-substring-search-backward ()</code><a class="copiable-link" href="#index-history_002dsubstring_002dsearch_002dbackward-_0028_0029"> &para;</a></span></dt>
<dd><p>Search backward through the history for the string of characters
between the start of the current line and the point.
The search string may match anywhere in a history line.
This is a non-incremental search.
By default, this command is unbound.
</p>
</dd>
<dt><a id="index-history_002dsubstring_002dsearch_002dforward-_0028_0029"></a><span><code class="code">history-substring-search-forward ()</code><a class="copiable-link" href="#index-history_002dsubstring_002dsearch_002dforward-_0028_0029"> &para;</a></span></dt>
<dd><p>Search forward through the history for the string of characters
between the start of the current line and the point.
The search string may match anywhere in a history line.
This is a non-incremental search.
By default, this command is unbound.
</p>
</dd>
<dt><a id="index-yank_002dnth_002darg-_0028M_002dC_002dy_0029"></a><span><code class="code">yank-nth-arg (M-C-y)</code><a class="copiable-link" href="#index-yank_002dnth_002darg-_0028M_002dC_002dy_0029"> &para;</a></span></dt>
<dd><p>Insert the first argument to the previous command (usually
the second word on the previous line) at point.
With an argument <var class="var">n</var>,
insert the <var class="var">n</var>th word from the previous command (the words
in the previous command begin with word 0).
A negative argument inserts the <var class="var">n</var>th word from the end of
the previous command.
Once the argument <var class="var">n</var> is computed,
this uses the history expansion facilities to extract the
<var class="var">n</var>th word, as if the
&lsquo;<samp class="samp">!<var class="var">n</var></samp>&rsquo; history expansion had been specified.
</p>
</dd>
<dt><a id="index-yank_002dlast_002darg-_0028M_002d_002e-or-M_002d_005f_0029"></a><span><code class="code">yank-last-arg (M-. or M-_)</code><a class="copiable-link" href="#index-yank_002dlast_002darg-_0028M_002d_002e-or-M_002d_005f_0029"> &para;</a></span></dt>
<dd><p>Insert last argument to the previous command (the last word of the
previous history entry).
With a numeric argument, behave exactly like <code class="code">yank-nth-arg</code>.
Successive calls to <code class="code">yank-last-arg</code> move back through the history
list, inserting the last word (or the word specified by the argument to
the first call) of each line in turn.
Any numeric argument supplied to these successive calls determines
the direction to move through the history.
A negative argument switches the direction through the history
(back or forward).
This uses the history expansion facilities to extract the
last  word, as if the
&lsquo;<samp class="samp">!$</samp>&rsquo; history expansion had been specified.
</p>
</dd>
<dt><a id="index-operate_002dand_002dget_002dnext-_0028C_002do_0029"></a><span><code class="code">operate-and-get-next (C-o)</code><a class="copiable-link" href="#index-operate_002dand_002dget_002dnext-_0028C_002do_0029"> &para;</a></span></dt>
<dd><p>Accept the current line for return to the calling application as if a
newline had been entered,
and fetch the next line relative to the current line from the history
for editing.
A numeric argument, if supplied, specifies the history entry
to use instead of the current line.
</p>
</dd>
<dt><a id="index-fetch_002dhistory-_0028_0029"></a><span><code class="code">fetch-history ()</code><a class="copiable-link" href="#index-fetch_002dhistory-_0028_0029"> &para;</a></span></dt>
<dd><p>With a numeric argument, fetch that entry from the history list
and make it the current line.
Without an argument, move back to the first entry in the history list.
</p>
</dd>
</dl>

<hr>
</div>
<div class="subsection-level-extent" id="Commands-For-Text">
<div class="nav-panel">
<p>
Next: <a href="#Commands-For-Killing" accesskey="n" rel="next">Killing And Yanking</a>, Previous: <a href="#Commands-For-History" accesskey="p" rel="prev">Commands For Manipulating The History</a>, Up: <a href="#Bindable-Readline-Commands" accesskey="u" rel="up">Bindable Readline Commands</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Commands-For-Changing-Text"><span>1.4.3 Commands For Changing Text<a class="copiable-link" href="#Commands-For-Changing-Text"> &para;</a></span></h4>

<dl class="ftable">
<dt><a id="index-end_002dof_002dfile-_0028usually-C_002dd_0029"></a><span><code class="code"><i class="i">end-of-file</i> (usually C-d)</code><a class="copiable-link" href="#index-end_002dof_002dfile-_0028usually-C_002dd_0029"> &para;</a></span></dt>
<dd><p>The character indicating end-of-file as set, for example, by
<code class="code">stty</code>.
If this character is read when there are no characters
on the line, and point is at the beginning of the line, Readline
interprets it as the end of input and returns <small class="sc">EOF</small>.
</p>
</dd>
<dt><a id="index-delete_002dchar-_0028C_002dd_0029"></a><span><code class="code">delete-char (C-d)</code><a class="copiable-link" href="#index-delete_002dchar-_0028C_002dd_0029"> &para;</a></span></dt>
<dd><p>Delete the character at point.
If this function is bound to the
same character as the tty <small class="sc">EOF</small> character, as <kbd class="kbd">C-d</kbd>
commonly is, see above for the effects.
This may also be bound to the Delete key on some keyboards.
</p>
</dd>
<dt><a id="index-backward_002ddelete_002dchar-_0028Rubout_0029"></a><span><code class="code">backward-delete-char (Rubout)</code><a class="copiable-link" href="#index-backward_002ddelete_002dchar-_0028Rubout_0029"> &para;</a></span></dt>
<dd><p>Delete the character behind the cursor.
A numeric argument means
to kill the characters, saving them on the kill ring,
instead of deleting them.
</p>
</dd>
<dt><a id="index-forward_002dbackward_002ddelete_002dchar-_0028_0029"></a><span><code class="code">forward-backward-delete-char ()</code><a class="copiable-link" href="#index-forward_002dbackward_002ddelete_002dchar-_0028_0029"> &para;</a></span></dt>
<dd><p>Delete the character under the cursor, unless the cursor is at the
end of the line, in which case the character behind the cursor is
deleted.
By default, this is not bound to a key.
</p>
</dd>
<dt><a id="index-quoted_002dinsert-_0028C_002dq-or-C_002dv_0029"></a><span><code class="code">quoted-insert (C-q or C-v)</code><a class="copiable-link" href="#index-quoted_002dinsert-_0028C_002dq-or-C_002dv_0029"> &para;</a></span></dt>
<dd><p>Add the next character typed to the line verbatim.
This is how to insert key sequences like <kbd class="kbd">C-q</kbd>, for example.
</p>
</dd>
<dt><a id="index-tab_002dinsert-_0028M_002dTAB_0029"></a><span><code class="code">tab-insert (M-<kbd class="key">TAB</kbd>)</code><a class="copiable-link" href="#index-tab_002dinsert-_0028M_002dTAB_0029"> &para;</a></span></dt>
<dd><p>Insert a tab character.
</p>
</dd>
<dt><a id="index-self_002dinsert-_0028a_002c-b_002c-A_002c-1_002c-_0021_002c-_2026_0029"></a><span><code class="code">self-insert (a, b, A, 1, !, &hellip;)</code><a class="copiable-link" href="#index-self_002dinsert-_0028a_002c-b_002c-A_002c-1_002c-_0021_002c-_2026_0029"> &para;</a></span></dt>
<dd><p>Insert the character typed.
</p>
</dd>
<dt><a id="index-bracketed_002dpaste_002dbegin-_0028_0029"></a><span><code class="code">bracketed-paste-begin ()</code><a class="copiable-link" href="#index-bracketed_002dpaste_002dbegin-_0028_0029"> &para;</a></span></dt>
<dd><p>This function is intended to be bound to the &quot;bracketed paste&quot; escape
sequence sent by some terminals, and such a binding is assigned by default.
It allows Readline to insert the pasted text as a single unit without treating
each character as if it had been read from the keyboard.
The characters
are inserted as if each one was bound to <code class="code">self-insert</code> instead of
executing any editing commands.
</p>
<p>Bracketed paste sets the region (the characters between point and the mark)
to the inserted text.
It sets the <em class="emph">active region</em>.
</p>
</dd>
<dt><a id="index-transpose_002dchars-_0028C_002dt_0029"></a><span><code class="code">transpose-chars (C-t)</code><a class="copiable-link" href="#index-transpose_002dchars-_0028C_002dt_0029"> &para;</a></span></dt>
<dd><p>Drag the character before the cursor forward over
the character at the cursor, moving the
cursor forward as well.
If the insertion point
is at the end of the line, then this
transposes the last two characters of the line.
Negative arguments have no effect.
</p>
</dd>
<dt><a id="index-transpose_002dwords-_0028M_002dt_0029"></a><span><code class="code">transpose-words (M-t)</code><a class="copiable-link" href="#index-transpose_002dwords-_0028M_002dt_0029"> &para;</a></span></dt>
<dd><p>Drag the word before point past the word after point,
moving point past that word as well.
If the insertion point is at the end of the line, this transposes
the last two words on the line.
</p>

</dd>
<dt><a id="index-upcase_002dword-_0028M_002du_0029"></a><span><code class="code">upcase-word (M-u)</code><a class="copiable-link" href="#index-upcase_002dword-_0028M_002du_0029"> &para;</a></span></dt>
<dd><p>Uppercase the current (or following) word.
With a negative argument,
uppercase the previous word, but do not move the cursor.
</p>
</dd>
<dt><a id="index-downcase_002dword-_0028M_002dl_0029"></a><span><code class="code">downcase-word (M-l)</code><a class="copiable-link" href="#index-downcase_002dword-_0028M_002dl_0029"> &para;</a></span></dt>
<dd><p>Lowercase the current (or following) word.
With a negative argument,
lowercase the previous word, but do not move the cursor.
</p>
</dd>
<dt><a id="index-capitalize_002dword-_0028M_002dc_0029"></a><span><code class="code">capitalize-word (M-c)</code><a class="copiable-link" href="#index-capitalize_002dword-_0028M_002dc_0029"> &para;</a></span></dt>
<dd><p>Capitalize the current (or following) word.
With a negative argument,
capitalize the previous word, but do not move the cursor.
</p>
</dd>
<dt><a id="index-overwrite_002dmode-_0028_0029"></a><span><code class="code">overwrite-mode ()</code><a class="copiable-link" href="#index-overwrite_002dmode-_0028_0029"> &para;</a></span></dt>
<dd><p>Toggle overwrite mode.
With an explicit positive numeric argument, switches to overwrite mode.
With an explicit non-positive numeric argument, switches to insert mode.
This command affects only <code class="code">emacs</code> mode;
<code class="code">vi</code> mode does overwrite differently.
Each call to <code class="code">readline()</code> starts in insert mode.
</p>
<p>In overwrite mode, characters bound to <code class="code">self-insert</code> replace
the text at point rather than pushing the text to the right.
Characters bound to <code class="code">backward-delete-char</code> replace the character
before point with a space.
</p>
<p>By default, this command is unbound, but may be bound to the Insert
key on some keyboards.
</p>
</dd>
</dl>

<hr>
</div>
<div class="subsection-level-extent" id="Commands-For-Killing">
<div class="nav-panel">
<p>
Next: <a href="#Numeric-Arguments" accesskey="n" rel="next">Specifying Numeric Arguments</a>, Previous: <a href="#Commands-For-Text" accesskey="p" rel="prev">Commands For Changing Text</a>, Up: <a href="#Bindable-Readline-Commands" accesskey="u" rel="up">Bindable Readline Commands</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Killing-And-Yanking"><span>1.4.4 Killing And Yanking<a class="copiable-link" href="#Killing-And-Yanking"> &para;</a></span></h4>

<dl class="ftable">
<dt><a id="index-kill_002dline-_0028C_002dk_0029"></a><span><code class="code">kill-line (C-k)</code><a class="copiable-link" href="#index-kill_002dline-_0028C_002dk_0029"> &para;</a></span></dt>
<dd><p>Kill the text from point to the end of the current line.
With a negative numeric argument, kill backward from the cursor to the
beginning of the line.
</p>
</dd>
<dt><a id="index-backward_002dkill_002dline-_0028C_002dx-Rubout_0029"></a><span><code class="code">backward-kill-line (C-x Rubout)</code><a class="copiable-link" href="#index-backward_002dkill_002dline-_0028C_002dx-Rubout_0029"> &para;</a></span></dt>
<dd><p>Kill backward from the cursor to the beginning of the current line.
With a negative numeric argument, kill forward from the cursor to the
end of the line.
</p>
</dd>
<dt><a id="index-unix_002dline_002ddiscard-_0028C_002du_0029"></a><span><code class="code">unix-line-discard (C-u)</code><a class="copiable-link" href="#index-unix_002dline_002ddiscard-_0028C_002du_0029"> &para;</a></span></dt>
<dd><p>Kill backward from the cursor to the beginning of the current line.
</p>
</dd>
<dt><a id="index-kill_002dwhole_002dline-_0028_0029"></a><span><code class="code">kill-whole-line ()</code><a class="copiable-link" href="#index-kill_002dwhole_002dline-_0028_0029"> &para;</a></span></dt>
<dd><p>Kill all characters on the current line, no matter where point is.
By default, this is unbound.
</p>
</dd>
<dt><a id="index-kill_002dword-_0028M_002dd_0029"></a><span><code class="code">kill-word (M-d)</code><a class="copiable-link" href="#index-kill_002dword-_0028M_002dd_0029"> &para;</a></span></dt>
<dd><p>Kill from point to the end of the current word, or if between
words, to the end of the next word.
Word boundaries are the same as <code class="code">forward-word</code>.
</p>
</dd>
<dt><a id="index-backward_002dkill_002dword-_0028M_002dDEL_0029"></a><span><code class="code">backward-kill-word (M-<kbd class="key">DEL</kbd>)</code><a class="copiable-link" href="#index-backward_002dkill_002dword-_0028M_002dDEL_0029"> &para;</a></span></dt>
<dd><p>Kill the word behind point.
Word boundaries are the same as <code class="code">backward-word</code>.
</p>

</dd>
<dt><a id="index-unix_002dword_002drubout-_0028C_002dw_0029"></a><span><code class="code">unix-word-rubout (C-w)</code><a class="copiable-link" href="#index-unix_002dword_002drubout-_0028C_002dw_0029"> &para;</a></span></dt>
<dd><p>Kill the word behind point, using white space as a word boundary,
saving the killed text on the kill-ring.
</p>
</dd>
<dt><a id="index-unix_002dfilename_002drubout-_0028_0029"></a><span><code class="code">unix-filename-rubout ()</code><a class="copiable-link" href="#index-unix_002dfilename_002drubout-_0028_0029"> &para;</a></span></dt>
<dd><p>Kill the word behind point, using white space and the slash character
as the word boundaries,
saving the killed text on the kill-ring.
</p>
</dd>
<dt><a id="index-delete_002dhorizontal_002dspace-_0028_0029"></a><span><code class="code">delete-horizontal-space ()</code><a class="copiable-link" href="#index-delete_002dhorizontal_002dspace-_0028_0029"> &para;</a></span></dt>
<dd><p>Delete all spaces and tabs around point.
By default, this is unbound.
</p>
</dd>
<dt><a id="index-kill_002dregion-_0028_0029"></a><span><code class="code">kill-region ()</code><a class="copiable-link" href="#index-kill_002dregion-_0028_0029"> &para;</a></span></dt>
<dd><p>Kill the text in the current region.
By default, this command is unbound.
</p>
</dd>
<dt><a id="index-copy_002dregion_002das_002dkill-_0028_0029"></a><span><code class="code">copy-region-as-kill ()</code><a class="copiable-link" href="#index-copy_002dregion_002das_002dkill-_0028_0029"> &para;</a></span></dt>
<dd><p>Copy the text in the region to the kill buffer, so it can be yanked
right away.
By default, this command is unbound.
</p>
</dd>
<dt><a id="index-copy_002dbackward_002dword-_0028_0029"></a><span><code class="code">copy-backward-word ()</code><a class="copiable-link" href="#index-copy_002dbackward_002dword-_0028_0029"> &para;</a></span></dt>
<dd><p>Copy the word before point to the kill buffer.
The word boundaries are the same as <code class="code">backward-word</code>.
By default, this command is unbound.
</p>
</dd>
<dt><a id="index-copy_002dforward_002dword-_0028_0029"></a><span><code class="code">copy-forward-word ()</code><a class="copiable-link" href="#index-copy_002dforward_002dword-_0028_0029"> &para;</a></span></dt>
<dd><p>Copy the word following point to the kill buffer.
The word boundaries are the same as <code class="code">forward-word</code>.
By default, this command is unbound.
</p>
</dd>
<dt><a id="index-yank-_0028C_002dy_0029"></a><span><code class="code">yank (C-y)</code><a class="copiable-link" href="#index-yank-_0028C_002dy_0029"> &para;</a></span></dt>
<dd><p>Yank the top of the kill ring into the buffer at point.
</p>
</dd>
<dt><a id="index-yank_002dpop-_0028M_002dy_0029"></a><span><code class="code">yank-pop (M-y)</code><a class="copiable-link" href="#index-yank_002dpop-_0028M_002dy_0029"> &para;</a></span></dt>
<dd><p>Rotate the kill-ring, and yank the new top.
You can only do this if
the prior command is <code class="code">yank</code> or <code class="code">yank-pop</code>.
</p></dd>
</dl>

<hr>
</div>
<div class="subsection-level-extent" id="Numeric-Arguments">
<div class="nav-panel">
<p>
Next: <a href="#Commands-For-Completion" accesskey="n" rel="next">Letting Readline Type For You</a>, Previous: <a href="#Commands-For-Killing" accesskey="p" rel="prev">Killing And Yanking</a>, Up: <a href="#Bindable-Readline-Commands" accesskey="u" rel="up">Bindable Readline Commands</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Specifying-Numeric-Arguments"><span>1.4.5 Specifying Numeric Arguments<a class="copiable-link" href="#Specifying-Numeric-Arguments"> &para;</a></span></h4>
<dl class="ftable">
<dt><a id="index-digit_002dargument-_0028M_002d0_002c-M_002d1_002c-_2026-M_002d_002d_0029"></a><span><code class="code">digit-argument (<kbd class="kbd">M-0</kbd>, <kbd class="kbd">M-1</kbd>, &hellip; <kbd class="kbd">M--</kbd>)</code><a class="copiable-link" href="#index-digit_002dargument-_0028M_002d0_002c-M_002d1_002c-_2026-M_002d_002d_0029"> &para;</a></span></dt>
<dd><p>Add this digit to the argument already accumulating, or start a new
argument.
<kbd class="kbd">M--</kbd> starts a negative argument.
</p>
</dd>
<dt><a id="index-universal_002dargument-_0028_0029"></a><span><code class="code">universal-argument ()</code><a class="copiable-link" href="#index-universal_002dargument-_0028_0029"> &para;</a></span></dt>
<dd><p>This is another way to specify an argument.
If this command is followed by one or more digits, optionally with a
leading minus sign, those digits define the argument.
If the command is followed by digits, executing <code class="code">universal-argument</code>
again ends the numeric argument, but is otherwise ignored.
As a special case, if this command is immediately followed by a
character that is neither a digit nor minus sign, the argument count
for the next command is multiplied by four.
The argument count is initially one, so executing this function the
first time makes the argument count four, a second time makes the
argument count sixteen, and so on.
By default, this is not bound to a key.
</p></dd>
</dl>

<hr>
</div>
<div class="subsection-level-extent" id="Commands-For-Completion">
<div class="nav-panel">
<p>
Next: <a href="#Keyboard-Macros" accesskey="n" rel="next">Keyboard Macros</a>, Previous: <a href="#Numeric-Arguments" accesskey="p" rel="prev">Specifying Numeric Arguments</a>, Up: <a href="#Bindable-Readline-Commands" accesskey="u" rel="up">Bindable Readline Commands</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Letting-Readline-Type-For-You"><span>1.4.6 Letting Readline Type For You<a class="copiable-link" href="#Letting-Readline-Type-For-You"> &para;</a></span></h4>

<dl class="ftable">
<dt><a id="index-complete-_0028TAB_0029"></a><span><code class="code">complete (<kbd class="key">TAB</kbd>)</code><a class="copiable-link" href="#index-complete-_0028TAB_0029"> &para;</a></span></dt>
<dd><p>Attempt to perform completion on the text before point.
The actual completion performed is application-specific.
The default is filename completion.
</p>
</dd>
<dt><a id="index-possible_002dcompletions-_0028M_002d_003f_0029"></a><span><code class="code">possible-completions (M-?)</code><a class="copiable-link" href="#index-possible_002dcompletions-_0028M_002d_003f_0029"> &para;</a></span></dt>
<dd><p>List the possible completions of the text before point.
When displaying completions, Readline sets the number of columns used
for display to the value of <code class="code">completion-display-width</code>, the value of
the environment variable <code class="env">COLUMNS</code>, or the screen width, in that order.
</p>
</dd>
<dt><a id="index-insert_002dcompletions-_0028M_002d_002a_0029"></a><span><code class="code">insert-completions (M-*)</code><a class="copiable-link" href="#index-insert_002dcompletions-_0028M_002d_002a_0029"> &para;</a></span></dt>
<dd><p>Insert all completions of the text before point that would have
been generated by <code class="code">possible-completions</code>,
separated by a space.
</p>
</dd>
<dt><a id="index-menu_002dcomplete-_0028_0029"></a><span><code class="code">menu-complete ()</code><a class="copiable-link" href="#index-menu_002dcomplete-_0028_0029"> &para;</a></span></dt>
<dd><p>Similar to <code class="code">complete</code>, but replaces the word to be completed
with a single match from the list of possible completions.
Repeatedly executing <code class="code">menu-complete</code> steps through the list
of possible completions, inserting each match in turn.
At the end of the list of completions,
<code class="code">menu-complete</code> rings the bell
(subject to the setting of <code class="code">bell-style</code>)
and restores the original text.
An argument of <var class="var">n</var> moves <var class="var">n</var> positions forward in the list
of matches; a negative argument moves backward through the list.
This command is intended to be bound to <kbd class="key">TAB</kbd>, but is unbound
by default.
</p>
</dd>
<dt><a id="index-menu_002dcomplete_002dbackward-_0028_0029"></a><span><code class="code">menu-complete-backward ()</code><a class="copiable-link" href="#index-menu_002dcomplete_002dbackward-_0028_0029"> &para;</a></span></dt>
<dd><p>Identical to <code class="code">menu-complete</code>, but moves backward through the list
of possible completions, as if <code class="code">menu-complete</code> had been given a
negative argument.
This command is unbound by default.
</p>
</dd>
<dt><a id="index-export_002dcompletions-_0028_0029"></a><span><code class="code">export-completions ()</code><a class="copiable-link" href="#index-export_002dcompletions-_0028_0029"> &para;</a></span></dt>
<dd><p>Perform completion on the word before point as described above
and write the list of possible completions to Readline&rsquo;s output stream
using the following format, writing information on separate lines:
</p>
<ul class="itemize mark-bullet">
<li>the number of matches <var class="var">N</var>;
</li><li>the word being completed;
</li><li><var class="var">S</var>:<var class="var">E</var>,
where <var class="var">S</var> and <var class="var">E</var> are the start and end offsets of the word
in the Readline line buffer; then
</li><li>each match, one per line
</li></ul>

<p>If there are no matches, the first line will be &ldquo;0&rdquo;,
and this command does not print any output after the <var class="var">S</var>:<var class="var">E</var>.
If there is only a single match, this prints a single line containing it.
If there is more than one match, this prints the common prefix of the
matches, which may be empty, on the first line after the <var class="var">S</var>:<var class="var">E</var>,
then the matches on subsequent lines.
In this case, <var class="var">N</var> will include the first line with the common prefix.
</p>
<p>The user or application
should be able to accommodate the possibility of a blank line.
The intent is that the user or application reads <var class="var">N</var> lines after
the line containing <var class="var">S</var>:<var class="var">E</var> to obtain the match list.
This command is unbound by default.
</p>
</dd>
<dt><a id="index-delete_002dchar_002dor_002dlist-_0028_0029"></a><span><code class="code">delete-char-or-list ()</code><a class="copiable-link" href="#index-delete_002dchar_002dor_002dlist-_0028_0029"> &para;</a></span></dt>
<dd><p>Deletes the character under the cursor if not at the beginning or
end of the line (like <code class="code">delete-char</code>).
At the end of the line, it behaves identically to <code class="code">possible-completions</code>.
This command is unbound by default.
</p>
</dd>
</dl>

<hr>
</div>
<div class="subsection-level-extent" id="Keyboard-Macros">
<div class="nav-panel">
<p>
Next: <a href="#Miscellaneous-Commands" accesskey="n" rel="next">Some Miscellaneous Commands</a>, Previous: <a href="#Commands-For-Completion" accesskey="p" rel="prev">Letting Readline Type For You</a>, Up: <a href="#Bindable-Readline-Commands" accesskey="u" rel="up">Bindable Readline Commands</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Keyboard-Macros-1"><span>1.4.7 Keyboard Macros<a class="copiable-link" href="#Keyboard-Macros-1"> &para;</a></span></h4>
<dl class="ftable">
<dt><a id="index-start_002dkbd_002dmacro-_0028C_002dx-_0028_0029"></a><span><code class="code">start-kbd-macro (C-x ()</code><a class="copiable-link" href="#index-start_002dkbd_002dmacro-_0028C_002dx-_0028_0029"> &para;</a></span></dt>
<dd><p>Begin saving the characters typed into the current keyboard macro.
</p>
</dd>
<dt><a id="index-end_002dkbd_002dmacro-_0028C_002dx-_0029_0029"></a><span><code class="code">end-kbd-macro (C-x ))</code><a class="copiable-link" href="#index-end_002dkbd_002dmacro-_0028C_002dx-_0029_0029"> &para;</a></span></dt>
<dd><p>Stop saving the characters typed into the current keyboard macro
and save the definition.
</p>
</dd>
<dt><a id="index-call_002dlast_002dkbd_002dmacro-_0028C_002dx-e_0029"></a><span><code class="code">call-last-kbd-macro (C-x e)</code><a class="copiable-link" href="#index-call_002dlast_002dkbd_002dmacro-_0028C_002dx-e_0029"> &para;</a></span></dt>
<dd><p>Re-execute the last keyboard macro defined, by making the characters
in the macro appear as if typed at the keyboard.
</p>
</dd>
<dt><a id="index-print_002dlast_002dkbd_002dmacro-_0028_0029"></a><span><code class="code">print-last-kbd-macro ()</code><a class="copiable-link" href="#index-print_002dlast_002dkbd_002dmacro-_0028_0029"> &para;</a></span></dt>
<dd><p>Print the last keyboard macro defined in a format suitable for the
<var class="var">inputrc</var> file.
</p>
</dd>
</dl>

<hr>
</div>
<div class="subsection-level-extent" id="Miscellaneous-Commands">
<div class="nav-panel">
<p>
Previous: <a href="#Keyboard-Macros" accesskey="p" rel="prev">Keyboard Macros</a>, Up: <a href="#Bindable-Readline-Commands" accesskey="u" rel="up">Bindable Readline Commands</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Some-Miscellaneous-Commands"><span>1.4.8 Some Miscellaneous Commands<a class="copiable-link" href="#Some-Miscellaneous-Commands"> &para;</a></span></h4>
<dl class="ftable">
<dt><a id="index-re_002dread_002dinit_002dfile-_0028C_002dx-C_002dr_0029"></a><span><code class="code">re-read-init-file (C-x C-r)</code><a class="copiable-link" href="#index-re_002dread_002dinit_002dfile-_0028C_002dx-C_002dr_0029"> &para;</a></span></dt>
<dd><p>Read in the contents of the <var class="var">inputrc</var> file, and incorporate
any bindings or variable assignments found there.
</p>
</dd>
<dt><a id="index-abort-_0028C_002dg_0029"></a><span><code class="code">abort (C-g)</code><a class="copiable-link" href="#index-abort-_0028C_002dg_0029"> &para;</a></span></dt>
<dd><p>Abort the current editing command and
ring the terminal&rsquo;s bell (subject to the setting of
<code class="code">bell-style</code>).
</p>
</dd>
<dt><a id="index-do_002dlowercase_002dversion-_0028M_002dA_002c-M_002dB_002c-M_002dx_002c-_2026_0029"></a><span><code class="code">do-lowercase-version (M-A, M-B, M-<var class="var">x</var>, &hellip;)</code><a class="copiable-link" href="#index-do_002dlowercase_002dversion-_0028M_002dA_002c-M_002dB_002c-M_002dx_002c-_2026_0029"> &para;</a></span></dt>
<dd><p>If the metafied character <var class="var">x</var> is upper case, run the command
that is bound to the corresponding metafied lower case character.
The behavior is undefined if <var class="var">x</var> is already lower case.
</p>
</dd>
<dt><a id="index-prefix_002dmeta-_0028ESC_0029"></a><span><code class="code">prefix-meta (<kbd class="key">ESC</kbd>)</code><a class="copiable-link" href="#index-prefix_002dmeta-_0028ESC_0029"> &para;</a></span></dt>
<dd><p>Metafy the next character typed.
Typing &lsquo;<samp class="samp"><kbd class="key">ESC</kbd> f</samp>&rsquo; is equivalent to typing <kbd class="kbd">M-f</kbd>.
</p>
</dd>
<dt><a id="index-undo-_0028C_002d_005f-or-C_002dx-C_002du_0029"></a><span><code class="code">undo (C-_ or C-x C-u)</code><a class="copiable-link" href="#index-undo-_0028C_002d_005f-or-C_002dx-C_002du_0029"> &para;</a></span></dt>
<dd><p>Incremental undo, separately remembered for each line.
</p>
</dd>
<dt><a id="index-revert_002dline-_0028M_002dr_0029"></a><span><code class="code">revert-line (M-r)</code><a class="copiable-link" href="#index-revert_002dline-_0028M_002dr_0029"> &para;</a></span></dt>
<dd><p>Undo all changes made to this line.
This is like executing the <code class="code">undo</code>
command enough times to get back to the initial state.
</p>
</dd>
<dt><a id="index-tilde_002dexpand-_0028M_002d_007e_0029"></a><span><code class="code">tilde-expand (M-~)</code><a class="copiable-link" href="#index-tilde_002dexpand-_0028M_002d_007e_0029"> &para;</a></span></dt>
<dd><p>Perform tilde expansion on the current word.
</p>
</dd>
<dt><a id="index-set_002dmark-_0028C_002d_0040_0029"></a><span><code class="code">set-mark (C-@)</code><a class="copiable-link" href="#index-set_002dmark-_0028C_002d_0040_0029"> &para;</a></span></dt>
<dd><p>Set the mark to the point.
If a numeric argument is supplied, set the mark to that position.
</p>
</dd>
<dt><a id="index-exchange_002dpoint_002dand_002dmark-_0028C_002dx-C_002dx_0029"></a><span><code class="code">exchange-point-and-mark (C-x C-x)</code><a class="copiable-link" href="#index-exchange_002dpoint_002dand_002dmark-_0028C_002dx-C_002dx_0029"> &para;</a></span></dt>
<dd><p>Swap the point with the mark.
Set the current cursor position to the saved position,
then set the mark to the old cursor position.
</p>
</dd>
<dt><a id="index-character_002dsearch-_0028C_002d_005d_0029"></a><span><code class="code">character-search (C-])</code><a class="copiable-link" href="#index-character_002dsearch-_0028C_002d_005d_0029"> &para;</a></span></dt>
<dd><p>Read a character and move point to the next occurrence of that character.
A negative argument searches for previous occurrences.
</p>
</dd>
<dt><a id="index-character_002dsearch_002dbackward-_0028M_002dC_002d_005d_0029"></a><span><code class="code">character-search-backward (M-C-])</code><a class="copiable-link" href="#index-character_002dsearch_002dbackward-_0028M_002dC_002d_005d_0029"> &para;</a></span></dt>
<dd><p>Read a character and move point to the previous occurrence of that character.
A negative argument searches for subsequent occurrences.
</p>
</dd>
<dt><a id="index-skip_002dcsi_002dsequence-_0028_0029"></a><span><code class="code">skip-csi-sequence ()</code><a class="copiable-link" href="#index-skip_002dcsi_002dsequence-_0028_0029"> &para;</a></span></dt>
<dd><p>Read enough characters to consume a multi-key sequence such as those
defined for keys like Home and End.
CSI sequences begin with a Control Sequence Indicator (CSI), usually
<kbd class="kbd">ESC [</kbd>.
If this sequence is bound to &quot;\e[&quot;,
keys producing CSI sequences have no effect
unless explicitly bound to a Readline command,
instead of inserting stray characters into the editing buffer.
This is unbound by default, but usually bound to
<kbd class="kbd">ESC [</kbd>.
</p>
</dd>
<dt><a id="index-insert_002dcomment-_0028M_002d_0023_0029"></a><span><code class="code">insert-comment (M-#)</code><a class="copiable-link" href="#index-insert_002dcomment-_0028M_002d_0023_0029"> &para;</a></span></dt>
<dd><p>Without a numeric argument, insert the value of the <code class="code">comment-begin</code>
variable at the beginning of the current line.
If a numeric argument is supplied, this command acts as a toggle:  if
the characters at the beginning of the line do not match the value
of <code class="code">comment-begin</code>, insert the value; otherwise delete
the characters in <code class="code">comment-begin</code> from the beginning of the line.
In either case, the line is accepted as if a newline had been typed.
</p>
</dd>
<dt><a id="index-dump_002dfunctions-_0028_0029"></a><span><code class="code">dump-functions ()</code><a class="copiable-link" href="#index-dump_002dfunctions-_0028_0029"> &para;</a></span></dt>
<dd><p>Print all of the functions and their key bindings
to the Readline output stream.
If a numeric argument is supplied,
the output is formatted in such a way that it can be made part
of an <var class="var">inputrc</var> file.
This command is unbound by default.
</p>
</dd>
<dt><a id="index-dump_002dvariables-_0028_0029"></a><span><code class="code">dump-variables ()</code><a class="copiable-link" href="#index-dump_002dvariables-_0028_0029"> &para;</a></span></dt>
<dd><p>Print all of the settable variables and their values
to the Readline output stream.
If a numeric argument is supplied,
the output is formatted in such a way that it can be made part
of an <var class="var">inputrc</var> file.
This command is unbound by default.
</p>
</dd>
<dt><a id="index-dump_002dmacros-_0028_0029"></a><span><code class="code">dump-macros ()</code><a class="copiable-link" href="#index-dump_002dmacros-_0028_0029"> &para;</a></span></dt>
<dd><p>Print all of the Readline key sequences bound to macros and the
strings they output
to the Readline output stream.
If a numeric argument is supplied,
the output is formatted in such a way that it can be made part
of an <var class="var">inputrc</var> file.
This command is unbound by default.
</p>
</dd>
<dt><a id="index-execute_002dnamed_002dcommand-_0028M_002dx_0029"></a><span><code class="code">execute-named-command (M-x)</code><a class="copiable-link" href="#index-execute_002dnamed_002dcommand-_0028M_002dx_0029"> &para;</a></span></dt>
<dd><p>Read a bindable Readline command name from the input and execute the
function to which it&rsquo;s bound, as if the key sequence to which it was
bound appeared in the input.
If this function is supplied with a numeric argument, it passes that
argument to the function it executes.
</p>

</dd>
<dt><a id="index-emacs_002dediting_002dmode-_0028C_002de_0029"></a><span><code class="code">emacs-editing-mode (C-e)</code><a class="copiable-link" href="#index-emacs_002dediting_002dmode-_0028C_002de_0029"> &para;</a></span></dt>
<dd><p>When in <code class="code">vi</code> command mode, this causes a switch to <code class="code">emacs</code>
editing mode.
</p>
</dd>
<dt><a id="index-vi_002dediting_002dmode-_0028M_002dC_002dj_0029"></a><span><code class="code">vi-editing-mode (M-C-j)</code><a class="copiable-link" href="#index-vi_002dediting_002dmode-_0028M_002dC_002dj_0029"> &para;</a></span></dt>
<dd><p>When in <code class="code">emacs</code> editing mode, this causes a switch to <code class="code">vi</code>
editing mode.
</p>

</dd>
</dl>

<hr>
</div>
</div>
<div class="section-level-extent" id="Readline-vi-Mode">
<div class="nav-panel">
<p>
Previous: <a href="#Bindable-Readline-Commands" accesskey="p" rel="prev">Bindable Readline Commands</a>, Up: <a href="#Command-Line-Editing" accesskey="u" rel="up">Command Line Editing</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h3 class="section" id="Readline-vi-Mode-1"><span>1.5 Readline vi Mode<a class="copiable-link" href="#Readline-vi-Mode-1"> &para;</a></span></h3>

<p>While the Readline library does not have a full set of <code class="code">vi</code>
editing functions, it does contain enough to allow simple editing
of the line.
The Readline <code class="code">vi</code> mode behaves as specified in the
<code class="code">sh</code> description in the <small class="sc">POSIX</small> standard.
</p>
<p>In order to switch interactively between <code class="code">emacs</code> and <code class="code">vi</code>
editing modes, use the command <kbd class="kbd">M-C-j</kbd> (bound to emacs-editing-mode
when in <code class="code">vi</code> mode and to vi-editing-mode in <code class="code">emacs</code> mode).
The Readline default is <code class="code">emacs</code> mode.
</p>
<p>When you enter a line in <code class="code">vi</code> mode, you are already placed in
&lsquo;insertion&rsquo; mode, as if you had typed an &lsquo;<samp class="samp">i</samp>&rsquo;.  Pressing <kbd class="key">ESC</kbd>
switches you into &lsquo;command&rsquo; mode, where you can edit the text of the
line with the standard <code class="code">vi</code> movement keys, move to previous
history lines with &lsquo;<samp class="samp">k</samp>&rsquo; and subsequent lines with &lsquo;<samp class="samp">j</samp>&rsquo;, and
so forth.
</p>


<hr>
</div>
</div>
<div class="chapter-level-extent" id="Programming-with-GNU-Readline">
<div class="nav-panel">
<p>
Next: <a href="#GNU-Free-Documentation-License" accesskey="n" rel="next">GNU Free Documentation License</a>, Previous: <a href="#Command-Line-Editing" accesskey="p" rel="prev">Command Line Editing</a>, Up: <a href="#Top" accesskey="u" rel="up">GNU Readline Library</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h2 class="chapter" id="Programming-with-GNU-Readline-1"><span>2 Programming with GNU Readline<a class="copiable-link" href="#Programming-with-GNU-Readline-1"> &para;</a></span></h2>

<p>This chapter describes the interface between the <small class="sc">GNU</small> Readline Library and
other programs.  If you are a programmer, and you wish to include the
features found in <small class="sc">GNU</small> Readline
such as completion, line editing, and interactive history manipulation
in your own programs, this section is for you.
</p>

<ul class="mini-toc">
<li><a href="#Basic-Behavior" accesskey="1">Basic Behavior</a></li>
<li><a href="#Custom-Functions" accesskey="2">Custom Functions</a></li>
<li><a href="#Readline-Variables" accesskey="3">Readline Variables</a></li>
<li><a href="#Readline-Convenience-Functions" accesskey="4">Readline Convenience Functions</a></li>
<li><a href="#Readline-Signal-Handling" accesskey="5">Readline Signal Handling</a></li>
<li><a href="#Custom-Completers" accesskey="6">Custom Completers</a></li>
</ul>
<hr>
<div class="section-level-extent" id="Basic-Behavior">
<div class="nav-panel">
<p>
Next: <a href="#Custom-Functions" accesskey="n" rel="next">Custom Functions</a>, Up: <a href="#Programming-with-GNU-Readline" accesskey="u" rel="up">Programming with GNU Readline</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h3 class="section" id="Basic-Behavior-1"><span>2.1 Basic Behavior<a class="copiable-link" href="#Basic-Behavior-1"> &para;</a></span></h3>

<p>Many programs provide a command line interface, such as <code class="code">mail</code>,
<code class="code">ftp</code>, and <code class="code">sh</code>.
For such programs, the default behavior of Readline is sufficient.
This section describes how to use Readline in
the simplest way possible, perhaps to replace calls in your code to
<code class="code">fgets()</code>.
</p>
<a class="index-entry-id" id="index-readline"></a>
<a class="index-entry-id" id="index-readline_002c-function"></a>

<p>The function <code class="code">readline()</code> prints a prompt <var class="var">prompt</var>
and then reads and returns a single line of text from the user.
Since it&rsquo;s possible to enter characters into the line while quoting
them to disable any Readline editing function they might normally have,
this line may include embedded newlines and other special characters.
If <var class="var">prompt</var> is <code class="code">NULL</code> or the empty string,
<code class="code">readline()</code> does not display a prompt.
The line <code class="code">readline()</code> returns is allocated with <code class="code">malloc()</code>;
the caller should <code class="code">free()</code> the line when it has finished with it.
The declaration for <code class="code">readline</code> in ANSI C is
</p>
<div class="example">
<pre class="example-preformatted"><code class="code">char *readline (const char *<var class="var">prompt</var>);</code>
</pre></div>

<p>So, one might say
</p><div class="example">
<pre class="example-preformatted"><code class="code">char *line = readline (&quot;Enter a line: &quot;);</code>
</pre></div>
<p>in order to read a line of text from the user.
The line returned has the final newline removed, so only the
text remains.
This means that lines consisting of a newline return the empty string.
</p>
<p>If Readline encounters an <code class="code">EOF</code> while reading the line,
and the line is empty at that point,
then <code class="code">readline()</code> returns <code class="code">(char *)NULL</code>.
Otherwise, the line is ended just as if a newline had been typed.
</p>
<p>Readline performs some expansion on the <var class="var">prompt</var> before it is
displayed on the screen.
See the description of <code class="code">rl_expand_prompt</code>
(see <a class="pxref" href="#Redisplay">Redisplay</a>) for additional details, especially if <var class="var">prompt</var>
will contain characters that do not consume physical screen space when
displayed.
</p>
<p>If you want the user to be able to get at the line later, (with
<kbd class="key">C-p</kbd> for example), you must call <code class="code">add_history()</code> to save the
line away in a <em class="dfn">history</em> list of such lines.
</p>
<div class="example">
<pre class="example-preformatted"><code class="code">add_history (line)</code>;
</pre></div>

<p>For full details on the GNU History Library, see the associated manual.
</p>
<p>It is preferable to avoid saving empty lines on the history list, since
users rarely have a burning need to reuse a blank line.
Here is a function which usefully replaces the standard <code class="code">gets()</code> library
function, and has the advantage of no static buffer to overflow:
</p>
<div class="example">
<pre class="example-preformatted">/* A static variable for holding the line. */
static char *line_read = (char *)NULL;

/* Read a string, and return a pointer to it.
   Returns NULL on EOF. */
char *
rl_gets ()
{
  /* If the buffer has already been allocated,
     return the memory to the free pool. */
  if (line_read)
    {
      free (line_read);
      line_read = (char *)NULL;
    }

  /* Get a line from the user. */
  line_read = readline (&quot;&quot;);

  /* If the line has any text in it,
     save it on the history. */
  if (line_read &amp;&amp; *line_read)
    add_history (line_read);

  return (line_read);
}
</pre></div>

<p>This function gives the user the default behavior of <kbd class="key">TAB</kbd>
completion: filename completion.
If you do not want Readline to
complete filenames, you can change the binding of the <kbd class="key">TAB</kbd> key
with <code class="code">rl_bind_key()</code>.
</p>
<div class="example">
<pre class="example-preformatted"><code class="code">int rl_bind_key (int <var class="var">key</var>, rl_command_func_t *<var class="var">function</var>);</code>
</pre></div>

<p><code class="code">rl_bind_key()</code> takes two arguments: <var class="var">key</var> is the character that
you want to bind, and <var class="var">function</var> is the address of the function to
call when <var class="var">key</var> is pressed.
Binding <kbd class="key">TAB</kbd> to <code class="code">rl_insert()</code> makes <kbd class="key">TAB</kbd> insert itself.
<code class="code">rl_bind_key()</code> returns non-zero if <var class="var">key</var> is not a valid
ASCII character code (between 0 and 255).
</p>
<p>Thus, to disable the default <kbd class="key">TAB</kbd> behavior, the following suffices:
</p><div class="example">
<pre class="example-preformatted"><code class="code">rl_bind_key ('\t', rl_insert);</code>
</pre></div>

<p>This code should be executed once at the start of your program; you
might write a function called <code class="code">initialize_readline()</code> which
performs this and other desired initializations, such as installing
custom completers (see <a class="pxref" href="#Custom-Completers">Custom Completers</a>).
</p>
<hr>
</div>
<div class="section-level-extent" id="Custom-Functions">
<div class="nav-panel">
<p>
Next: <a href="#Readline-Variables" accesskey="n" rel="next">Readline Variables</a>, Previous: <a href="#Basic-Behavior" accesskey="p" rel="prev">Basic Behavior</a>, Up: <a href="#Programming-with-GNU-Readline" accesskey="u" rel="up">Programming with GNU Readline</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h3 class="section" id="Custom-Functions-1"><span>2.2 Custom Functions<a class="copiable-link" href="#Custom-Functions-1"> &para;</a></span></h3>

<p>Readline provides many functions for manipulating the text of
the line, but it isn&rsquo;t possible to anticipate the needs of all
programs.
This section describes the various functions and variables
defined within the Readline library which allow a program to add
customized functionality to Readline.
</p>
<p>Before declaring any functions that customize Readline&rsquo;s behavior, or
using any functionality Readline provides in other code, an
application writer should include the file <code class="code">&lt;readline/readline.h&gt;</code>
in any file that uses Readline&rsquo;s features.
Since some of the definitions
in <code class="code">readline.h</code> use the <code class="code">stdio</code> library, the program
should include the file <code class="code">&lt;stdio.h&gt;</code>
before <code class="code">readline.h</code>.
</p>
<p><code class="code">readline.h</code> defines a C preprocessor variable that should
be treated as an integer, <code class="code">RL_READLINE_VERSION</code>, which may
be used to conditionally compile application code depending on
the installed Readline version.
The value is a hexadecimal
encoding of the major and minor version numbers of the library,
of the form 0x<var class="var">MMmm</var>.  <var class="var">MM</var> is the two-digit major
version number; <var class="var">mm</var> is the two-digit minor version number. 
For Readline 4.2, for example, the value of
<code class="code">RL_READLINE_VERSION</code> would be <code class="code">0x0402</code>. 
</p>

<ul class="mini-toc">
<li><a href="#Readline-Typedefs" accesskey="1">Readline Typedefs</a></li>
<li><a href="#Function-Writing" accesskey="2">Writing a New Function</a></li>
</ul>
<hr>
<div class="subsection-level-extent" id="Readline-Typedefs">
<div class="nav-panel">
<p>
Next: <a href="#Function-Writing" accesskey="n" rel="next">Writing a New Function</a>, Up: <a href="#Custom-Functions" accesskey="u" rel="up">Custom Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Readline-Typedefs-1"><span>2.2.1 Readline Typedefs<a class="copiable-link" href="#Readline-Typedefs-1"> &para;</a></span></h4>

<p>For readability, we declare a number of new object types, all pointers
to functions.
</p>
<p>The reason for declaring these new types is to make it easier to write
code describing pointers to C functions with appropriately prototyped
arguments and return values.
</p>
<p>For instance, say we want to declare a variable <var class="var">func</var> as a pointer
to a function which takes two <code class="code">int</code> arguments and returns an
<code class="code">int</code> (this is the type of all of the Readline bindable functions).
Instead of the classic C declaration
</p>
<p><code class="code">int (*func)();</code>
</p>
<p>or the ANSI-C style declaration
</p>
<p><code class="code">int (*func)(int, int);</code>
</p>
<p>we may write
</p>
<p><code class="code">rl_command_func_t *func;</code>
</p>
<p>The full list of function pointer types available is
</p>
<dl class="table">
<dt><code class="code">typedef int rl_command_func_t (int, int);</code></dt>
<dt><code class="code">typedef char *rl_compentry_func_t (const char *, int);</code></dt>
<dt><code class="code">typedef char **rl_completion_func_t (const char *, int, int);</code></dt>
<dt><code class="code">typedef char *rl_quote_func_t (char *, int, char *);</code></dt>
<dt><code class="code">typedef char *rl_dequote_func_t (char *, int);</code></dt>
<dt><code class="code">typedef int rl_compignore_func_t (char **);</code></dt>
<dt><code class="code">typedef void rl_compdisp_func_t (char **, int, int);</code></dt>
<dt><code class="code">typedef void rl_macro_print_func_t (const char *, const char *, int, const char *);</code></dt>
<dt><code class="code">typedef int rl_hook_func_t (void);</code></dt>
<dt><code class="code">typedef int rl_getc_func_t (FILE *);</code></dt>
<dt><code class="code">typedef int rl_linebuf_func_t (char *, int);</code></dt>
<dt><code class="code">typedef int rl_intfunc_t (int);</code></dt>
<dt><code class="code">#define rl_ivoidfunc_t rl_hook_func_t</code></dt>
<dt><code class="code">typedef int rl_icpfunc_t (char *);</code></dt>
<dt><code class="code">typedef int rl_icppfunc_t (char **);</code></dt>
<dt><code class="code">typedef void rl_voidfunc_t (void);</code></dt>
<dt><code class="code">typedef void rl_vintfunc_t (int);</code></dt>
<dt><code class="code">typedef void rl_vcpfunc_t (char *);</code></dt>
<dt><code class="code">typedef void rl_vcppfunc_t (char **);</code></dt>
</dl>

<p>The <samp class="file">rltypedefs.h</samp> file has more documentation for these types.
</p>
<hr>
</div>
<div class="subsection-level-extent" id="Function-Writing">
<div class="nav-panel">
<p>
Previous: <a href="#Readline-Typedefs" accesskey="p" rel="prev">Readline Typedefs</a>, Up: <a href="#Custom-Functions" accesskey="u" rel="up">Custom Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Writing-a-New-Function"><span>2.2.2 Writing a New Function<a class="copiable-link" href="#Writing-a-New-Function"> &para;</a></span></h4>

<p>In order to write new functions for Readline, you need to know the
calling conventions for keyboard-invoked functions, and the names of the
variables that describe the current state of the line read so far.
</p>
<p>The calling sequence for a command <code class="code">foo</code> looks like
</p>
<div class="example">
<pre class="example-preformatted"><code class="code">int foo (int count, int key)</code>
</pre></div>

<p>where <var class="var">count</var> is the numeric argument (or 1 if defaulted) and
<var class="var">key</var> is the key that invoked this function.
</p>
<p>It is completely up to the function as to what should be done with the
numeric argument.
Some functions use it as a repeat count, some
as a flag, and others to choose alternate behavior (refreshing the current
line as opposed to refreshing the screen, for example).
Some choose to ignore it.
In general, if a
function uses the numeric argument as a repeat count, it should be able
to do something useful with both negative and positive arguments.
At the very least, it should be aware that it can be passed a
negative argument.
</p>
<p>A command function should return 0 if its action completes successfully,
and a value greater than zero if some error occurs.
All of the builtin Readline bindable command functions
obey this convention.
</p>
<hr>
</div>
</div>
<div class="section-level-extent" id="Readline-Variables">
<div class="nav-panel">
<p>
Next: <a href="#Readline-Convenience-Functions" accesskey="n" rel="next">Readline Convenience Functions</a>, Previous: <a href="#Custom-Functions" accesskey="p" rel="prev">Custom Functions</a>, Up: <a href="#Programming-with-GNU-Readline" accesskey="u" rel="up">Programming with GNU Readline</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h3 class="section" id="Readline-Variables-1"><span>2.3 Readline Variables<a class="copiable-link" href="#Readline-Variables-1"> &para;</a></span></h3>

<p>These variables are available to function writers.
</p>
<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fline_005fbuffer"><span class="category-def">Variable: </span><span><code class="def-type">char *</code> <strong class="def-name">rl_line_buffer</strong><a class="copiable-link" href="#index-rl_005fline_005fbuffer"> &para;</a></span></dt>
<dd><p>This is the line gathered so far.
You are welcome to modify the contents of the line,
but see <a class="ref" href="#Allowing-Undoing">Allowing Undoing</a>.
The function <code class="code">rl_extend_line_buffer</code> will increase
the memory allocated to <code class="code">rl_line_buffer</code>.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fpoint"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_point</strong><a class="copiable-link" href="#index-rl_005fpoint"> &para;</a></span></dt>
<dd><p>The offset of the current cursor position in <code class="code">rl_line_buffer</code>
(the <em class="emph">point</em>).
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fend"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_end</strong><a class="copiable-link" href="#index-rl_005fend"> &para;</a></span></dt>
<dd><p>The number of characters present in <code class="code">rl_line_buffer</code>.
When <code class="code">rl_point</code> is at the end of the line,
<code class="code">rl_point</code> and <code class="code">rl_end</code> are equal.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fmark"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_mark</strong><a class="copiable-link" href="#index-rl_005fmark"> &para;</a></span></dt>
<dd><p>The <var class="var">mark</var> (saved position) in the current line.
If set, the mark and point define a <em class="emph">region</em>.
Some Readline commands set the mark as part of operating;
users can also set the mark explicitly.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fdone"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_done</strong><a class="copiable-link" href="#index-rl_005fdone"> &para;</a></span></dt>
<dd><p>Setting this to a non-zero value causes Readline to return the current
line immediately.
Readline will set this variable when it has read a key sequence bound
to <code class="code">accept-line</code> and is about to return the line to the caller.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005feof_005ffound"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_eof_found</strong><a class="copiable-link" href="#index-rl_005feof_005ffound"> &para;</a></span></dt>
<dd><p>Readline will set this variable when it has read an EOF character
(e.g., the stty &lsquo;<samp class="samp">EOF</samp>&rsquo; character) on an empty line
or has encountered a read error or EOF and 
is about to return a NULL line to the caller.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fnum_005fchars_005fto_005fread"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_num_chars_to_read</strong><a class="copiable-link" href="#index-rl_005fnum_005fchars_005fto_005fread"> &para;</a></span></dt>
<dd><p>Setting this to a positive value before calling <code class="code">readline()</code> causes
Readline to return after accepting that many characters, rather
than reading up to a character bound to <code class="code">accept-line</code>.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fpending_005finput"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_pending_input</strong><a class="copiable-link" href="#index-rl_005fpending_005finput"> &para;</a></span></dt>
<dd><p>Setting this to a value makes it the next keystroke read.
This is a way to stuff a single character into the input stream.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fdispatching"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_dispatching</strong><a class="copiable-link" href="#index-rl_005fdispatching"> &para;</a></span></dt>
<dd><p>Set to a non-zero value if a function is being called from a key binding;
zero otherwise.
Application functions can test this to discover whether
they were called directly or by Readline&rsquo;s dispatching mechanism.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005ferase_005fempty_005fline"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_erase_empty_line</strong><a class="copiable-link" href="#index-rl_005ferase_005fempty_005fline"> &para;</a></span></dt>
<dd><p>Setting this to a non-zero value causes Readline to completely erase
the current line, including any prompt, any time a newline is typed as
the only character on an otherwise-empty line.
This moves the cursor to the beginning of the newly-blank line.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fprompt"><span class="category-def">Variable: </span><span><code class="def-type">char *</code> <strong class="def-name">rl_prompt</strong><a class="copiable-link" href="#index-rl_005fprompt"> &para;</a></span></dt>
<dd><p>The prompt Readline uses.
This is set from the argument to
<code class="code">readline()</code>, and should not be assigned to directly.
The <code class="code">rl_set_prompt()</code> function (see <a class="pxref" href="#Redisplay">Redisplay</a>) may
be used to modify the prompt string after calling <code class="code">readline()</code>.
Readline performs some prompt expansions and analyzes the prompt for
line breaks, so <code class="code">rl_set_prompt()</code> is preferred.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fdisplay_005fprompt"><span class="category-def">Variable: </span><span><code class="def-type">char *</code> <strong class="def-name">rl_display_prompt</strong><a class="copiable-link" href="#index-rl_005fdisplay_005fprompt"> &para;</a></span></dt>
<dd><p>The string displayed as the prompt.
This is usually identical to
<var class="var">rl_prompt</var>, but may be changed temporarily by functions that
use the prompt string as a message area, such as incremental search.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005falready_005fprompted"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_already_prompted</strong><a class="copiable-link" href="#index-rl_005falready_005fprompted"> &para;</a></span></dt>
<dd><p>If an application wishes to display the prompt itself, rather than have
Readline do it the first time <code class="code">readline()</code> is called, it should set
this variable to a non-zero value after displaying the prompt.
The prompt must also be passed as the argument to <code class="code">readline()</code> so
the redisplay functions can update the display properly.
The calling application is responsible for managing the value; Readline
never sets it.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005flibrary_005fversion"><span class="category-def">Variable: </span><span><code class="def-type">const char *</code> <strong class="def-name">rl_library_version</strong><a class="copiable-link" href="#index-rl_005flibrary_005fversion"> &para;</a></span></dt>
<dd><p>The version number of this revision of the Readline library, as a string
(e.g., &quot;4.2&quot;).
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005freadline_005fversion"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_readline_version</strong><a class="copiable-link" href="#index-rl_005freadline_005fversion"> &para;</a></span></dt>
<dd><p>An integer encoding the current version of the library.
The encoding is of the form 0x<var class="var">MMmm</var>,
where <var class="var">MM</var> is the two-digit major version number,
and <var class="var">mm</var> is the two-digit minor version number.
For example, for Readline-4.2, <code class="code">rl_readline_version</code> would have the
value 0x0402.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fgnu_005freadline_005fp"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_gnu_readline_p</strong><a class="copiable-link" href="#index-rl_005fgnu_005freadline_005fp"> &para;</a></span></dt>
<dd><p>Always set to 1, denoting that this is <small class="sc">GNU</small> Readline rather than some
emulation.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fterminal_005fname"><span class="category-def">Variable: </span><span><code class="def-type">const char *</code> <strong class="def-name">rl_terminal_name</strong><a class="copiable-link" href="#index-rl_005fterminal_005fname"> &para;</a></span></dt>
<dd><p>The terminal type, used for initialization.
If not set by the application,
Readline sets this to the value of the <code class="env">TERM</code> environment variable
the first time it is called.
Readline uses this to look up the terminal capabilities it needs in
the terminfo database.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005freadline_005fname"><span class="category-def">Variable: </span><span><code class="def-type">const char *</code> <strong class="def-name">rl_readline_name</strong><a class="copiable-link" href="#index-rl_005freadline_005fname"> &para;</a></span></dt>
<dd><p>This variable is set to a unique name by each application using Readline.
The value allows conditional parsing of the inputrc file
(see <a class="pxref" href="#Conditional-Init-Constructs">Conditional Init Constructs</a>).
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005finstream"><span class="category-def">Variable: </span><span><code class="def-type">FILE *</code> <strong class="def-name">rl_instream</strong><a class="copiable-link" href="#index-rl_005finstream"> &para;</a></span></dt>
<dd><p>The stdio stream from which Readline reads input.
If <code class="code">NULL</code>, Readline defaults to <var class="var">stdin</var>.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005foutstream"><span class="category-def">Variable: </span><span><code class="def-type">FILE *</code> <strong class="def-name">rl_outstream</strong><a class="copiable-link" href="#index-rl_005foutstream"> &para;</a></span></dt>
<dd><p>The stdio stream to which Readline performs output.
If <code class="code">NULL</code>, Readline defaults to <var class="var">stdout</var>.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fprefer_005fenv_005fwinsize"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_prefer_env_winsize</strong><a class="copiable-link" href="#index-rl_005fprefer_005fenv_005fwinsize"> &para;</a></span></dt>
<dd><p>If non-zero, Readline gives values found in the <code class="env">LINES</code> and
<code class="env">COLUMNS</code> environment variables greater precedence than values fetched
from the kernel when computing the screen dimensions.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005flast_005ffunc"><span class="category-def">Variable: </span><span><code class="def-type">rl_command_func_t *</code> <strong class="def-name">rl_last_func</strong><a class="copiable-link" href="#index-rl_005flast_005ffunc"> &para;</a></span></dt>
<dd><p>The address of the last command function Readline executed.
This may be used to test whether or not a function is being executed
twice in succession, for example.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fstartup_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_hook_func_t *</code> <strong class="def-name">rl_startup_hook</strong><a class="copiable-link" href="#index-rl_005fstartup_005fhook"> &para;</a></span></dt>
<dd><p>If non-zero, this is the address of a function to call just
before Readline prints the first prompt.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fpre_005finput_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_hook_func_t *</code> <strong class="def-name">rl_pre_input_hook</strong><a class="copiable-link" href="#index-rl_005fpre_005finput_005fhook"> &para;</a></span></dt>
<dd><p>If non-zero, this is the address of a function to call after
the first prompt has been printed and just before Readline
starts reading input characters.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fevent_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_hook_func_t *</code> <strong class="def-name">rl_event_hook</strong><a class="copiable-link" href="#index-rl_005fevent_005fhook"> &para;</a></span></dt>
<dd><p>If non-zero, this is the address of a function to call periodically
when Readline is waiting for terminal input.
By default, this will be called at most ten times a second if there
is no keyboard input.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fgetc_005ffunction"><span class="category-def">Variable: </span><span><code class="def-type">rl_getc_func_t *</code> <strong class="def-name">rl_getc_function</strong><a class="copiable-link" href="#index-rl_005fgetc_005ffunction"> &para;</a></span></dt>
<dd><p>If non-zero, Readline will call indirectly through this pointer
to get a character from the input stream.
By default, it is set to <code class="code">rl_getc</code>, the Readline character
input function (see <a class="pxref" href="#Character-Input">Character Input</a>).
In general, an application that sets <var class="var">rl_getc_function</var> should consider
setting <var class="var">rl_input_available_hook</var> as well.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fsignal_005fevent_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_hook_func_t *</code> <strong class="def-name">rl_signal_event_hook</strong><a class="copiable-link" href="#index-rl_005fsignal_005fevent_005fhook"> &para;</a></span></dt>
<dd><p>If non-zero, this is the address of a function to call if a read system
call is interrupted by a signal when Readline is reading terminal input.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005ftimeout_005fevent_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_hook_func_t *</code> <strong class="def-name">rl_timeout_event_hook</strong><a class="copiable-link" href="#index-rl_005ftimeout_005fevent_005fhook"> &para;</a></span></dt>
<dd><p>If non-zero, this is the address of a function to call if Readline times
out while reading input.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005finput_005favailable_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_hook_func_t *</code> <strong class="def-name">rl_input_available_hook</strong><a class="copiable-link" href="#index-rl_005finput_005favailable_005fhook"> &para;</a></span></dt>
<dd><p>If non-zero, Readline will use this function&rsquo;s return value when it needs
to determine whether or not there is available input on the current input
source.
The default hook checks <code class="code">rl_instream</code>; if an application is using a
different input source, it should set the hook appropriately.
Readline queries for available input when implementing intra-key-sequence
timeouts during input and incremental searches.
This function must return zero if there is no input available, and non-zero
if input is available.
This may use an application-specific timeout before returning a value;
Readline uses the value passed to <code class="code">rl_set_keyboard_input_timeout()</code>
or the value of the user-settable <var class="var">keyseq-timeout</var> variable.
This is designed for use by applications using Readline&rsquo;s callback interface
(see <a class="pxref" href="#Alternate-Interface">Alternate Interface</a>), which may not use the traditional
<code class="code">read(2)</code> and file descriptor interface, or other applications using
a different input mechanism.
If an application uses an input mechanism or hook that can potentially exceed
the value of <var class="var">keyseq-timeout</var>, it should increase the timeout or set
this hook appropriately even when not using the callback interface.
In general, an application that sets <var class="var">rl_getc_function</var> should consider
setting <var class="var">rl_input_available_hook</var> as well.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fredisplay_005ffunction"><span class="category-def">Variable: </span><span><code class="def-type">rl_voidfunc_t *</code> <strong class="def-name">rl_redisplay_function</strong><a class="copiable-link" href="#index-rl_005fredisplay_005ffunction"> &para;</a></span></dt>
<dd><p>Readline will call indirectly through this pointer
to update the display with the current contents of the editing buffer.
By default, it is set to <code class="code">rl_redisplay</code>, the default Readline
redisplay function (see <a class="pxref" href="#Redisplay">Redisplay</a>).
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fprep_005fterm_005ffunction"><span class="category-def">Variable: </span><span><code class="def-type">rl_vintfunc_t *</code> <strong class="def-name">rl_prep_term_function</strong><a class="copiable-link" href="#index-rl_005fprep_005fterm_005ffunction"> &para;</a></span></dt>
<dd><p>If non-zero, Readline will call indirectly through this pointer
to initialize the terminal.
The function takes a single argument, an
<code class="code">int</code> flag that says whether or not to use eight-bit characters.
By default, this is set to <code class="code">rl_prep_terminal</code>
(see <a class="pxref" href="#Terminal-Management">Terminal Management</a>).
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fdeprep_005fterm_005ffunction"><span class="category-def">Variable: </span><span><code class="def-type">rl_voidfunc_t *</code> <strong class="def-name">rl_deprep_term_function</strong><a class="copiable-link" href="#index-rl_005fdeprep_005fterm_005ffunction"> &para;</a></span></dt>
<dd><p>If non-zero, Readline will call indirectly through this pointer
to reset the terminal.
This function should undo the effects of <code class="code">rl_prep_term_function</code>.
By default, this is set to <code class="code">rl_deprep_terminal</code>
(see <a class="pxref" href="#Terminal-Management">Terminal Management</a>).
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fmacro_005fdisplay_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">void</code> <strong class="def-name">rl_macro_display_hook</strong><a class="copiable-link" href="#index-rl_005fmacro_005fdisplay_005fhook"> &para;</a></span></dt>
<dd><p>If set, this points to a function that <code class="code">rl_macro_dumper</code> will call to
display a key sequence bound to a macro.
It is called with the key sequence, the &quot;untranslated&quot; macro value (i.e.,
with backslash escapes included, as when passed to <code class="code">rl_macro_bind</code>),
the <code class="code">readable</code> argument passed to <code class="code">rl_macro_dumper</code>, and any
prefix to display before the key sequence.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fexecuting_005fkeymap"><span class="category-def">Variable: </span><span><code class="def-type">Keymap</code> <strong class="def-name">rl_executing_keymap</strong><a class="copiable-link" href="#index-rl_005fexecuting_005fkeymap"> &para;</a></span></dt>
<dd><p>This variable is set to the keymap (see <a class="pxref" href="#Keymaps">Selecting a Keymap</a>) in which the
currently executing Readline function was found.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fbinding_005fkeymap"><span class="category-def">Variable: </span><span><code class="def-type">Keymap</code> <strong class="def-name">rl_binding_keymap</strong><a class="copiable-link" href="#index-rl_005fbinding_005fkeymap"> &para;</a></span></dt>
<dd><p>This variable is set to the keymap (see <a class="pxref" href="#Keymaps">Selecting a Keymap</a>) in which the
last key binding occurred.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fexecuting_005fmacro"><span class="category-def">Variable: </span><span><code class="def-type">char *</code> <strong class="def-name">rl_executing_macro</strong><a class="copiable-link" href="#index-rl_005fexecuting_005fmacro"> &para;</a></span></dt>
<dd><p>This variable is set to the text of any currently-executing macro.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fexecuting_005fkey"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_executing_key</strong><a class="copiable-link" href="#index-rl_005fexecuting_005fkey"> &para;</a></span></dt>
<dd><p>The key that caused the dispatch to the currently-executing Readline function.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fexecuting_005fkeyseq"><span class="category-def">Variable: </span><span><code class="def-type">char *</code> <strong class="def-name">rl_executing_keyseq</strong><a class="copiable-link" href="#index-rl_005fexecuting_005fkeyseq"> &para;</a></span></dt>
<dd><p>The full key sequence that caused the dispatch to the currently-executing
Readline function.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fkey_005fsequence_005flength"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_key_sequence_length</strong><a class="copiable-link" href="#index-rl_005fkey_005fsequence_005flength"> &para;</a></span></dt>
<dd><p>The number of characters in <var class="var">rl_executing_keyseq</var>.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005freadline_005fstate"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_readline_state</strong><a class="copiable-link" href="#index-rl_005freadline_005fstate"> &para;</a></span></dt>
<dd><p>A variable with bit values that encapsulate the current Readline state.
A bit is set with the <code class="code">RL_SETSTATE</code> macro, and unset with the
<code class="code">RL_UNSETSTATE</code> macro.
Use the <code class="code">RL_ISSTATE</code> macro to test whether a particular state
bit is set.
Current state bits include:
</p>
<dl class="table">
<dt><code class="code">RL_STATE_NONE</code></dt>
<dd><p>Readline has not yet been called, nor has it begun to initialize.
</p></dd>
<dt><code class="code">RL_STATE_INITIALIZING</code></dt>
<dd><p>Readline is initializing its internal data structures.
</p></dd>
<dt><code class="code">RL_STATE_INITIALIZED</code></dt>
<dd><p>Readline has completed its initialization.
</p></dd>
<dt><code class="code">RL_STATE_TERMPREPPED</code></dt>
<dd><p>Readline has modified the terminal modes to do its own input and redisplay.
</p></dd>
<dt><code class="code">RL_STATE_READCMD</code></dt>
<dd><p>Readline is reading a command from the keyboard.
</p></dd>
<dt><code class="code">RL_STATE_METANEXT</code></dt>
<dd><p>Readline is reading more input after reading the meta-prefix character.
</p></dd>
<dt><code class="code">RL_STATE_DISPATCHING</code></dt>
<dd><p>Readline is dispatching to a command.
</p></dd>
<dt><code class="code">RL_STATE_MOREINPUT</code></dt>
<dd><p>Readline is reading more input while executing an editing command.
</p></dd>
<dt><code class="code">RL_STATE_ISEARCH</code></dt>
<dd><p>Readline is performing an incremental history search.
</p></dd>
<dt><code class="code">RL_STATE_NSEARCH</code></dt>
<dd><p>Readline is performing a non-incremental history search.
</p></dd>
<dt><code class="code">RL_STATE_SEARCH</code></dt>
<dd><p>Readline is searching backward or forward through the history for a string.
</p></dd>
<dt><code class="code">RL_STATE_NUMERICARG</code></dt>
<dd><p>Readline is reading a numeric argument.
</p></dd>
<dt><code class="code">RL_STATE_MACROINPUT</code></dt>
<dd><p>Readline is currently getting its input from a previously-defined keyboard
macro.
</p></dd>
<dt><code class="code">RL_STATE_MACRODEF</code></dt>
<dd><p>Readline is currently reading characters defining a keyboard macro.
</p></dd>
<dt><code class="code">RL_STATE_OVERWRITE</code></dt>
<dd><p>Readline is in overwrite mode.
</p></dd>
<dt><code class="code">RL_STATE_COMPLETING</code></dt>
<dd><p>Readline is performing word completion.
</p></dd>
<dt><code class="code">RL_STATE_SIGHANDLER</code></dt>
<dd><p>Readline is currently executing the Readline signal handler.
</p></dd>
<dt><code class="code">RL_STATE_UNDOING</code></dt>
<dd><p>Readline is performing an undo.
</p></dd>
<dt><code class="code">RL_STATE_INPUTPENDING</code></dt>
<dd><p>Readline has input pending due to a call to <code class="code">rl_execute_next()</code>.
</p></dd>
<dt><code class="code">RL_STATE_TTYCSAVED</code></dt>
<dd><p>Readline has saved the values of the terminal&rsquo;s special characters.
</p></dd>
<dt><code class="code">RL_STATE_CALLBACK</code></dt>
<dd><p>Readline is currently using the alternate (callback) interface
(see <a class="pxref" href="#Alternate-Interface">Alternate Interface</a>).
</p></dd>
<dt><code class="code">RL_STATE_VIMOTION</code></dt>
<dd><p>Readline is reading the argument to a vi-mode &quot;motion&quot; command.
</p></dd>
<dt><code class="code">RL_STATE_MULTIKEY</code></dt>
<dd><p>Readline is reading a multiple-keystroke command.
</p></dd>
<dt><code class="code">RL_STATE_VICMDONCE</code></dt>
<dd><p>Readline has entered vi command (movement) mode at least one time during
the current call to <code class="code">readline()</code>.
</p></dd>
<dt><code class="code">RL_STATE_DONE</code></dt>
<dd><p>Readline has read a key sequence bound to <code class="code">accept-line</code>
and is about to return the line to the caller.
</p></dd>
<dt><code class="code">RL_STATE_TIMEOUT</code></dt>
<dd><p>Readline has timed out (it did not receive a line or specified number of
characters before the timeout duration specified by <code class="code">rl_set_timeout</code>
elapsed) and is returning that status to the caller.
</p></dd>
<dt><code class="code">RL_STATE_EOF</code></dt>
<dd><p>Readline has read an EOF character (e.g., the stty &lsquo;<samp class="samp">EOF</samp>&rsquo; character)
or encountered a read error or EOF
and is about to return a NULL line to the caller.
</p></dd>
</dl>

</dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fexplicit_005farg"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_explicit_arg</strong><a class="copiable-link" href="#index-rl_005fexplicit_005farg"> &para;</a></span></dt>
<dd><p>Set to a non-zero value if an explicit numeric argument was specified by
the user.
It is only valid in a bindable command function.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fnumeric_005farg"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_numeric_arg</strong><a class="copiable-link" href="#index-rl_005fnumeric_005farg"> &para;</a></span></dt>
<dd><p>Set to the value of any numeric argument explicitly specified by the user
before executing the current Readline function.
It is only valid in a bindable command function.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fediting_005fmode"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_editing_mode</strong><a class="copiable-link" href="#index-rl_005fediting_005fmode"> &para;</a></span></dt>
<dd><p>Set to a value denoting Readline&rsquo;s current editing mode.
A value of <var class="var">1</var> means Readline is currently in emacs mode;
<var class="var">0</var> means that vi mode is active.
This determines the current keymap and key bindings.
</p></dd></dl>

<hr>
</div>
<div class="section-level-extent" id="Readline-Convenience-Functions">
<div class="nav-panel">
<p>
Next: <a href="#Readline-Signal-Handling" accesskey="n" rel="next">Readline Signal Handling</a>, Previous: <a href="#Readline-Variables" accesskey="p" rel="prev">Readline Variables</a>, Up: <a href="#Programming-with-GNU-Readline" accesskey="u" rel="up">Programming with GNU Readline</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h3 class="section" id="Readline-Convenience-Functions-1"><span>2.4 Readline Convenience Functions<a class="copiable-link" href="#Readline-Convenience-Functions-1"> &para;</a></span></h3>


<ul class="mini-toc">
<li><a href="#Function-Naming" accesskey="1">Naming a Function</a></li>
<li><a href="#Keymaps" accesskey="2">Selecting a Keymap</a></li>
<li><a href="#Binding-Keys" accesskey="3">Binding Keys</a></li>
<li><a href="#Associating-Function-Names-and-Bindings" accesskey="4">Associating Function Names and Bindings</a></li>
<li><a href="#Allowing-Undoing" accesskey="5">Allowing Undoing</a></li>
<li><a href="#Redisplay" accesskey="6">Redisplay</a></li>
<li><a href="#Modifying-Text" accesskey="7">Modifying Text</a></li>
<li><a href="#Character-Input" accesskey="8">Character Input</a></li>
<li><a href="#Terminal-Management" accesskey="9">Terminal Management</a></li>
<li><a href="#Utility-Functions">Utility Functions</a></li>
<li><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></li>
<li><a href="#Alternate-Interface">Alternate Interface</a></li>
<li><a href="#A-Readline-Example">A Readline Example</a></li>
<li><a href="#Alternate-Interface-Example">Alternate Interface Example</a></li>
</ul>
<hr>
<div class="subsection-level-extent" id="Function-Naming">
<div class="nav-panel">
<p>
Next: <a href="#Keymaps" accesskey="n" rel="next">Selecting a Keymap</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Naming-a-Function"><span>2.4.1 Naming a Function<a class="copiable-link" href="#Naming-a-Function"> &para;</a></span></h4>

<p>Readline has a descriptive
string name for every function a user can bind to a key sequence,
so users can dynamically change the bindings associated with key
sequences while using Readline,
using the descriptive name when referring to the function.
Thus, in an init file, one might find
</p>
<div class="example">
<pre class="example-preformatted">Meta-Rubout:	backward-kill-word
</pre></div>

<p>This binds the keystroke <kbd class="key">Meta-Rubout</kbd> to the function
<em class="emph">descriptively</em> named <code class="code">backward-kill-word</code>.
As the programmer, you
should bind the functions you write to descriptive names as well.
Readline provides a function for doing that:
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fadd_005fdefun"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_add_defun</strong> <code class="def-code-arguments">(const char *name, rl_command_func_t *function, int key)</code><a class="copiable-link" href="#index-rl_005fadd_005fdefun"> &para;</a></span></dt>
<dd><p>Add <var class="var">name</var> to the list of named functions.
Make <var class="var">function</var> be the function that gets called by key sequences
that bind to <var class="var">name</var>.
If <var class="var">key</var> is not -1, then bind it to
<var class="var">function</var> using <code class="code">rl_bind_key()</code>.
</p></dd></dl>

<p>Using this function alone is sufficient for most applications.
It is the recommended way to add a few functions to the default
functions that Readline has built in.
If you need to do something other than adding a function to Readline,
you may need to use the underlying functions described below.
</p>
<hr>
</div>
<div class="subsection-level-extent" id="Keymaps">
<div class="nav-panel">
<p>
Next: <a href="#Binding-Keys" accesskey="n" rel="next">Binding Keys</a>, Previous: <a href="#Function-Naming" accesskey="p" rel="prev">Naming a Function</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Selecting-a-Keymap"><span>2.4.2 Selecting a Keymap<a class="copiable-link" href="#Selecting-a-Keymap"> &para;</a></span></h4>

<p>Key bindings take place on a <em class="dfn">keymap</em>.
The keymap is the association between the keys that the user types and
the functions that get run.
You can make your own keymaps, copy existing keymaps, and tell
Readline which keymap to use.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fmake_005fbare_005fkeymap"><span class="category-def">Function: </span><span><code class="def-type">Keymap</code> <strong class="def-name">rl_make_bare_keymap</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fmake_005fbare_005fkeymap"> &para;</a></span></dt>
<dd><p>Returns a new, empty keymap.
The space for the keymap is allocated with
<code class="code">malloc()</code>; the caller should free it by calling
<code class="code">rl_free_keymap()</code> when done.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcopy_005fkeymap"><span class="category-def">Function: </span><span><code class="def-type">Keymap</code> <strong class="def-name">rl_copy_keymap</strong> <code class="def-code-arguments">(Keymap map)</code><a class="copiable-link" href="#index-rl_005fcopy_005fkeymap"> &para;</a></span></dt>
<dd><p>Return a new keymap which is a copy of <var class="var">map</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fmake_005fkeymap"><span class="category-def">Function: </span><span><code class="def-type">Keymap</code> <strong class="def-name">rl_make_keymap</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fmake_005fkeymap"> &para;</a></span></dt>
<dd><p>Return a new keymap with the printing characters bound to rl_insert,
the lowercase Meta characters bound to run their equivalents, and
the Meta digits bound to produce numeric arguments.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fdiscard_005fkeymap"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_discard_keymap</strong> <code class="def-code-arguments">(Keymap keymap)</code><a class="copiable-link" href="#index-rl_005fdiscard_005fkeymap"> &para;</a></span></dt>
<dd><p>Free the storage associated with the data in <var class="var">keymap</var>.
The caller should free <var class="var">keymap</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ffree_005fkeymap"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_free_keymap</strong> <code class="def-code-arguments">(Keymap keymap)</code><a class="copiable-link" href="#index-rl_005ffree_005fkeymap"> &para;</a></span></dt>
<dd><p>Free all storage associated with <var class="var">keymap</var>.
This calls <code class="code">rl_discard_keymap</code> to free subordinate
keymaps and macros.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fempty_005fkeymap"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_empty_keymap</strong> <code class="def-code-arguments">(Keymap keymap)</code><a class="copiable-link" href="#index-rl_005fempty_005fkeymap"> &para;</a></span></dt>
<dd><p>Return non-zero if there are no keys bound to functions in <var class="var">keymap</var>;
zero if there are any keys bound.
</p></dd></dl>

<p>Readline has several internal keymaps.
These functions allow you to change which keymap is active.
This is one way to switch editing modes, for example.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fget_005fkeymap"><span class="category-def">Function: </span><span><code class="def-type">Keymap</code> <strong class="def-name">rl_get_keymap</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fget_005fkeymap"> &para;</a></span></dt>
<dd><p>Returns the currently active keymap.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fset_005fkeymap"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_set_keymap</strong> <code class="def-code-arguments">(Keymap keymap)</code><a class="copiable-link" href="#index-rl_005fset_005fkeymap"> &para;</a></span></dt>
<dd><p>Makes <var class="var">keymap</var> the currently active keymap.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fget_005fkeymap_005fby_005fname"><span class="category-def">Function: </span><span><code class="def-type">Keymap</code> <strong class="def-name">rl_get_keymap_by_name</strong> <code class="def-code-arguments">(const char *name)</code><a class="copiable-link" href="#index-rl_005fget_005fkeymap_005fby_005fname"> &para;</a></span></dt>
<dd><p>Return the keymap matching <var class="var">name</var>.
<var class="var">name</var> is one which would be supplied in a
<code class="code">set keymap</code> inputrc line (see <a class="pxref" href="#Readline-Init-File">Readline Init File</a>).
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fget_005fkeymap_005fname"><span class="category-def">Function: </span><span><code class="def-type">char *</code> <strong class="def-name">rl_get_keymap_name</strong> <code class="def-code-arguments">(Keymap keymap)</code><a class="copiable-link" href="#index-rl_005fget_005fkeymap_005fname"> &para;</a></span></dt>
<dd><p>Return the name matching <var class="var">keymap</var>.
<var class="var">name</var> is one which would be supplied in a
<code class="code">set keymap</code> inputrc line (see <a class="pxref" href="#Readline-Init-File">Readline Init File</a>).
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fset_005fkeymap_005fname"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_set_keymap_name</strong> <code class="def-code-arguments">(const char *name, Keymap keymap)</code><a class="copiable-link" href="#index-rl_005fset_005fkeymap_005fname"> &para;</a></span></dt>
<dd><p>Set the name of <var class="var">keymap</var>.
This name will then be &quot;registered&quot; and
available for use in a <code class="code">set keymap</code> inputrc directive
see <a class="pxref" href="#Readline-Init-File">Readline Init File</a>).
The <var class="var">name</var> may not be one of Readline&rsquo;s builtin keymap names;
you may not add a different name for one of Readline&rsquo;s builtin keymaps.
You may replace the name associated with a given keymap by calling this
function more than once with the same <var class="var">keymap</var> argument.
You may associate a registered <var class="var">name</var> with a new keymap by calling this
function more than once  with the same <var class="var">name</var> argument.
There is no way to remove a named keymap once the name has been
registered.
Readline will make a copy of <var class="var">name</var>.
The return value is greater than zero unless <var class="var">name</var> is one of
Readline&rsquo;s builtin keymap names or <var class="var">keymap</var> is one of Readline&rsquo;s
builtin keymaps.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Binding-Keys">
<div class="nav-panel">
<p>
Next: <a href="#Associating-Function-Names-and-Bindings" accesskey="n" rel="next">Associating Function Names and Bindings</a>, Previous: <a href="#Keymaps" accesskey="p" rel="prev">Selecting a Keymap</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Binding-Keys-1"><span>2.4.3 Binding Keys<a class="copiable-link" href="#Binding-Keys-1"> &para;</a></span></h4>

<p>Key sequences are associated with functions through the keymap.
Readline has several internal keymaps: <code class="code">emacs_standard_keymap</code>,
<code class="code">emacs_meta_keymap</code>, <code class="code">emacs_ctlx_keymap</code>,
<code class="code">vi_movement_keymap</code>, and <code class="code">vi_insertion_keymap</code>.
<code class="code">emacs_standard_keymap</code> is the default, and the examples in
this manual assume that.
</p>
<p>Since <code class="code">readline()</code> installs a set of default key bindings the first
time it is called, there is always the danger that a custom binding
installed before the first call to <code class="code">readline()</code> will be overridden.
An alternate mechanism that can avoid this
is to install custom key bindings in an
initialization function assigned to the <code class="code">rl_startup_hook</code> variable
(see <a class="pxref" href="#Readline-Variables">Readline Variables</a>).
</p>
<p>These functions manage key bindings.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fbind_005fkey"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_bind_key</strong> <code class="def-code-arguments">(int key, rl_command_func_t *function)</code><a class="copiable-link" href="#index-rl_005fbind_005fkey"> &para;</a></span></dt>
<dd><p>Binds <var class="var">key</var> to <var class="var">function</var> in the currently active keymap.
Returns non-zero in the case of an invalid <var class="var">key</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fbind_005fkey_005fin_005fmap"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_bind_key_in_map</strong> <code class="def-code-arguments">(int key, rl_command_func_t *function, Keymap map)</code><a class="copiable-link" href="#index-rl_005fbind_005fkey_005fin_005fmap"> &para;</a></span></dt>
<dd><p>Bind <var class="var">key</var> to <var class="var">function</var> in <var class="var">map</var>.
Returns non-zero in the case of an invalid <var class="var">key</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fbind_005fkey_005fif_005funbound"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_bind_key_if_unbound</strong> <code class="def-code-arguments">(int key, rl_command_func_t *function)</code><a class="copiable-link" href="#index-rl_005fbind_005fkey_005fif_005funbound"> &para;</a></span></dt>
<dd><p>Binds <var class="var">key</var> to <var class="var">function</var> if it is not already bound in the
currently active keymap.
Returns non-zero in the case of an invalid <var class="var">key</var> or if <var class="var">key</var> is
already bound.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fbind_005fkey_005fif_005funbound_005fin_005fmap"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_bind_key_if_unbound_in_map</strong> <code class="def-code-arguments">(int key, rl_command_func_t *function, Keymap map)</code><a class="copiable-link" href="#index-rl_005fbind_005fkey_005fif_005funbound_005fin_005fmap"> &para;</a></span></dt>
<dd><p>Binds <var class="var">key</var> to <var class="var">function</var> if it is not already bound in <var class="var">map</var>.
Returns non-zero in the case of an invalid <var class="var">key</var> or if <var class="var">key</var> is
already bound.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005funbind_005fkey"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_unbind_key</strong> <code class="def-code-arguments">(int key)</code><a class="copiable-link" href="#index-rl_005funbind_005fkey"> &para;</a></span></dt>
<dd><p>Bind <var class="var">key</var> to the null function in the currently active keymap.
This is not the same as binding it to <code class="code">self-insert</code>.
Returns non-zero in case of error.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005funbind_005fkey_005fin_005fmap"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_unbind_key_in_map</strong> <code class="def-code-arguments">(int key, Keymap map)</code><a class="copiable-link" href="#index-rl_005funbind_005fkey_005fin_005fmap"> &para;</a></span></dt>
<dd><p>Bind <var class="var">key</var> to the null function in <var class="var">map</var>.
This is not the same as binding it to <code class="code">self-insert</code>.
Returns non-zero in case of error.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005funbind_005ffunction_005fin_005fmap"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_unbind_function_in_map</strong> <code class="def-code-arguments">(rl_command_func_t *function, Keymap map)</code><a class="copiable-link" href="#index-rl_005funbind_005ffunction_005fin_005fmap"> &para;</a></span></dt>
<dd><p>Unbind all keys that execute <var class="var">function</var> in <var class="var">map</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005funbind_005fcommand_005fin_005fmap"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_unbind_command_in_map</strong> <code class="def-code-arguments">(const char *command, Keymap map)</code><a class="copiable-link" href="#index-rl_005funbind_005fcommand_005fin_005fmap"> &para;</a></span></dt>
<dd><p>Unbind all keys that are bound to <var class="var">command</var> in <var class="var">map</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fbind_005fkeyseq"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_bind_keyseq</strong> <code class="def-code-arguments">(const char *keyseq, rl_command_func_t *function)</code><a class="copiable-link" href="#index-rl_005fbind_005fkeyseq"> &para;</a></span></dt>
<dd><p>Bind the key sequence represented by the string <var class="var">keyseq</var> to the function
<var class="var">function</var>, beginning in the current keymap.
This makes new keymaps as necessary.
The return value is non-zero if <var class="var">keyseq</var> is invalid.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fbind_005fkeyseq_005fin_005fmap"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_bind_keyseq_in_map</strong> <code class="def-code-arguments">(const char *keyseq, rl_command_func_t *function, Keymap map)</code><a class="copiable-link" href="#index-rl_005fbind_005fkeyseq_005fin_005fmap"> &para;</a></span></dt>
<dd><p>Bind the key sequence represented by the string <var class="var">keyseq</var> to the function
<var class="var">function</var> in <var class="var">map</var>.
This makes new keymaps as necessary.
Initial bindings are performed in <var class="var">map</var>.
The return value is non-zero if <var class="var">keyseq</var> is invalid.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fset_005fkey"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_set_key</strong> <code class="def-code-arguments">(const char *keyseq, rl_command_func_t *function, Keymap map)</code><a class="copiable-link" href="#index-rl_005fset_005fkey"> &para;</a></span></dt>
<dd><p>Equivalent to <code class="code">rl_bind_keyseq_in_map</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fbind_005fkeyseq_005fif_005funbound"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_bind_keyseq_if_unbound</strong> <code class="def-code-arguments">(const char *keyseq, rl_command_func_t *function)</code><a class="copiable-link" href="#index-rl_005fbind_005fkeyseq_005fif_005funbound"> &para;</a></span></dt>
<dd><p>Binds <var class="var">keyseq</var> to <var class="var">function</var> if it is not already bound in the
currently active keymap.
Returns non-zero in the case of an invalid <var class="var">keyseq</var> or if <var class="var">keyseq</var> is
already bound.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fbind_005fkeyseq_005fif_005funbound_005fin_005fmap"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_bind_keyseq_if_unbound_in_map</strong> <code class="def-code-arguments">(const char *keyseq, rl_command_func_t *function, Keymap map)</code><a class="copiable-link" href="#index-rl_005fbind_005fkeyseq_005fif_005funbound_005fin_005fmap"> &para;</a></span></dt>
<dd><p>Binds <var class="var">keyseq</var> to <var class="var">function</var> if it is not already bound in <var class="var">map</var>.
Returns non-zero in the case of an invalid <var class="var">keyseq</var> or if <var class="var">keyseq</var> is
already bound.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fgeneric_005fbind"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_generic_bind</strong> <code class="def-code-arguments">(int type, const char *keyseq, char *data, Keymap map)</code><a class="copiable-link" href="#index-rl_005fgeneric_005fbind"> &para;</a></span></dt>
<dd><p>Bind the key sequence represented by the string <var class="var">keyseq</var> to the arbitrary
pointer <var class="var">data</var>.
<var class="var">type</var> says what kind of data is pointed to by <var class="var">data</var>; this can be
a function (<code class="code">ISFUNC</code>),
a macro (<code class="code">ISMACR</code>),
or a keymap (<code class="code">ISKMAP</code>).
This makes new keymaps as necessary.
The initial keymap in which to do bindings is <var class="var">map</var>.
Returns non-zero in the case of an invalid <var class="var">keyseq</var>, zero otherwise.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fparse_005fand_005fbind"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_parse_and_bind</strong> <code class="def-code-arguments">(char *line)</code><a class="copiable-link" href="#index-rl_005fparse_005fand_005fbind"> &para;</a></span></dt>
<dd><p>Parse <var class="var">line</var> as if it had been read from the <code class="code">inputrc</code> file and
perform any key bindings and variable assignments found
(see <a class="pxref" href="#Readline-Init-File">Readline Init File</a>).
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fread_005finit_005ffile"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_read_init_file</strong> <code class="def-code-arguments">(const char *filename)</code><a class="copiable-link" href="#index-rl_005fread_005finit_005ffile"> &para;</a></span></dt>
<dd><p>Read keybindings and variable assignments from <var class="var">filename</var>
(see <a class="pxref" href="#Readline-Init-File">Readline Init File</a>).
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Associating-Function-Names-and-Bindings">
<div class="nav-panel">
<p>
Next: <a href="#Allowing-Undoing" accesskey="n" rel="next">Allowing Undoing</a>, Previous: <a href="#Binding-Keys" accesskey="p" rel="prev">Binding Keys</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Associating-Function-Names-and-Bindings-1"><span>2.4.4 Associating Function Names and Bindings<a class="copiable-link" href="#Associating-Function-Names-and-Bindings-1"> &para;</a></span></h4>

<p>These functions allow you to find out what keys invoke named functions
and the functions invoked by a particular key sequence.
You may also associate a new function name with an arbitrary function.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fnamed_005ffunction"><span class="category-def">Function: </span><span><code class="def-type">rl_command_func_t *</code> <strong class="def-name">rl_named_function</strong> <code class="def-code-arguments">(const char *name)</code><a class="copiable-link" href="#index-rl_005fnamed_005ffunction"> &para;</a></span></dt>
<dd><p>Return the function with name <var class="var">name</var>.
<var class="var">name</var> is a descriptive name users might use in a key binding.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ffunction_005fof_005fkeyseq"><span class="category-def">Function: </span><span><code class="def-type">rl_command_func_t *</code> <strong class="def-name">rl_function_of_keyseq</strong> <code class="def-code-arguments">(const char *keyseq, Keymap map, int *type)</code><a class="copiable-link" href="#index-rl_005ffunction_005fof_005fkeyseq"> &para;</a></span></dt>
<dd><p>Return the function invoked by <var class="var">keyseq</var> in keymap <var class="var">map</var>.
If <var class="var">map</var> is <code class="code">NULL</code>, this uses the current keymap.
If <var class="var">type</var> is not <code class="code">NULL</code>, this returns the type of the object
in the <code class="code">int</code> variable it points to
(one of <code class="code">ISFUNC</code>, <code class="code">ISKMAP</code>, or <code class="code">ISMACR</code>).
It takes a &quot;translated&quot; key sequence and should not be used
if the key sequence can include NUL.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ffunction_005fof_005fkeyseq_005flen"><span class="category-def">Function: </span><span><code class="def-type">rl_command_func_t *</code> <strong class="def-name">rl_function_of_keyseq_len</strong> <code class="def-code-arguments">(const char *keyseq, size_t len, Keymap map, int *type)</code><a class="copiable-link" href="#index-rl_005ffunction_005fof_005fkeyseq_005flen"> &para;</a></span></dt>
<dd><p>Return the function invoked by <var class="var">keyseq</var> of length <var class="var">len</var>
in keymap <var class="var">map</var>.
Equivalent to <code class="code">rl_function_of_keyseq</code> with the addition
of the <var class="var">len</var> parameter.
It takes a &quot;translated&quot; key sequence and should be used
if the key sequence can include NUL.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ftrim_005farg_005ffrom_005fkeyseq"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_trim_arg_from_keyseq</strong> <code class="def-code-arguments">(const char *keyseq, size_t len, Keymap map)</code><a class="copiable-link" href="#index-rl_005ftrim_005farg_005ffrom_005fkeyseq"> &para;</a></span></dt>
<dd><p>If there is a numeric argument at the beginning of <var class="var">keyseq</var>, possibly
including digits, return the index of the first character in <var class="var">keyseq</var>
following the numeric argument.
This can be used to skip over the numeric argument (which is available as
<code class="code">rl_numeric_arg</code>) while traversing the key sequence that invoked the
current command.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005finvoking_005fkeyseqs"><span class="category-def">Function: </span><span><code class="def-type">char **</code> <strong class="def-name">rl_invoking_keyseqs</strong> <code class="def-code-arguments">(rl_command_func_t *function)</code><a class="copiable-link" href="#index-rl_005finvoking_005fkeyseqs"> &para;</a></span></dt>
<dd><p>Return an array of strings representing the key sequences used to
invoke <var class="var">function</var> in the current keymap.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005finvoking_005fkeyseqs_005fin_005fmap"><span class="category-def">Function: </span><span><code class="def-type">char **</code> <strong class="def-name">rl_invoking_keyseqs_in_map</strong> <code class="def-code-arguments">(rl_command_func_t *function, Keymap map)</code><a class="copiable-link" href="#index-rl_005finvoking_005fkeyseqs_005fin_005fmap"> &para;</a></span></dt>
<dd><p>Return an array of strings representing the key sequences used to
invoke <var class="var">function</var> in the keymap <var class="var">map</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fprint_005fkeybinding"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_print_keybinding</strong> <code class="def-code-arguments">(const char *name, Keymap map, int readable)</code><a class="copiable-link" href="#index-rl_005fprint_005fkeybinding"> &para;</a></span></dt>
<dd><p>Print key sequences bound to Readline function name <var class="var">name</var> in
keymap <var class="var">map</var>.
If <var class="var">map</var> is NULL, this uses the current keymap.
If <var class="var">readable</var> is non-zero,
the list is formatted in such a way that it can be made part of an
<code class="code">inputrc</code> file and re-read to recreate the key binding.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ffunction_005fdumper"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_function_dumper</strong> <code class="def-code-arguments">(int readable)</code><a class="copiable-link" href="#index-rl_005ffunction_005fdumper"> &para;</a></span></dt>
<dd><p>Print the Readline function names and the key sequences currently
bound to them to <code class="code">rl_outstream</code>.
If <var class="var">readable</var> is non-zero,
the list is formatted in such a way that it can be made part of an
<code class="code">inputrc</code> file and re-read.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005flist_005ffunmap_005fnames"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_list_funmap_names</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005flist_005ffunmap_005fnames"> &para;</a></span></dt>
<dd><p>Print the names of all bindable Readline functions to <code class="code">rl_outstream</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ffunmap_005fnames"><span class="category-def">Function: </span><span><code class="def-type">const char **</code> <strong class="def-name">rl_funmap_names</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005ffunmap_005fnames"> &para;</a></span></dt>
<dd><p>Return a NULL terminated array of known function names.
The array is sorted. 
The array itself is allocated, but not the strings inside.
You should free the array, but not the pointers, using <code class="code">free</code>
or <code class="code">rl_free</code> when you are done.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fadd_005ffunmap_005fentry"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_add_funmap_entry</strong> <code class="def-code-arguments">(const char *name, rl_command_func_t *function)</code><a class="copiable-link" href="#index-rl_005fadd_005ffunmap_005fentry"> &para;</a></span></dt>
<dd><p>Add <var class="var">name</var> to the list of bindable Readline command names, and make
<var class="var">function</var> the function to be called when <var class="var">name</var> is invoked.
This returns the index of the newly-added <var class="var">name</var> in the array of
function names.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Allowing-Undoing">
<div class="nav-panel">
<p>
Next: <a href="#Redisplay" accesskey="n" rel="next">Redisplay</a>, Previous: <a href="#Associating-Function-Names-and-Bindings" accesskey="p" rel="prev">Associating Function Names and Bindings</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Allowing-Undoing-1"><span>2.4.5 Allowing Undoing<a class="copiable-link" href="#Allowing-Undoing-1"> &para;</a></span></h4>

<p>Supporting the undo command is a painless thing, and makes your
functions much more useful.
It is certainly easier to try something if you know you can undo it.
</p>
<p>If your function simply inserts text once, or deletes text once,
and uses <code class="code">rl_insert_text()</code> or <code class="code">rl_delete_text()</code> to do it,
then Readline does the undoing for you automatically.
</p>
<p>If you do multiple insertions or multiple deletions, or any combination
of these operations, you should group them together into one operation.
This is done with <code class="code">rl_begin_undo_group()</code> and
<code class="code">rl_end_undo_group()</code>.
</p>
<p>The types of events Readline can undo are:
</p>
<div class="example smallexample">
<pre class="example-preformatted">enum undo_code { UNDO_DELETE, UNDO_INSERT, UNDO_BEGIN, UNDO_END }; 
</pre></div>

<p>Notice that <code class="code">UNDO_DELETE</code> means to insert some text, and
<code class="code">UNDO_INSERT</code> means to delete some text.
That is, the undo code tells what to undo, not how to undo it.
<code class="code">UNDO_BEGIN</code> and <code class="code">UNDO_END</code> are tags
added by <code class="code">rl_begin_undo_group()</code> and <code class="code">rl_end_undo_group()</code>;
they are how Readline delimits groups of commands that should be
undone together.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fbegin_005fundo_005fgroup"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_begin_undo_group</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fbegin_005fundo_005fgroup"> &para;</a></span></dt>
<dd><p>Begins saving undo information in a group construct.
The undo information usually comes from calls to <code class="code">rl_insert_text()</code>
and <code class="code">rl_delete_text()</code>, but could be the result of calls to
<code class="code">rl_add_undo()</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fend_005fundo_005fgroup"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_end_undo_group</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fend_005fundo_005fgroup"> &para;</a></span></dt>
<dd><p>Closes the current undo group started with <code class="code">rl_begin_undo_group()</code>.
There should be one call to <code class="code">rl_end_undo_group()</code>
for each call to <code class="code">rl_begin_undo_group()</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fadd_005fundo"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_add_undo</strong> <code class="def-code-arguments">(enum undo_code what, int start, int end, char *text)</code><a class="copiable-link" href="#index-rl_005fadd_005fundo"> &para;</a></span></dt>
<dd><p>Remember how to undo an event (according to <var class="var">what</var>).
The affected text runs from <var class="var">start</var> to <var class="var">end</var>,
and encompasses <var class="var">text</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ffree_005fundo_005flist"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_free_undo_list</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005ffree_005fundo_005flist"> &para;</a></span></dt>
<dd><p>Free the existing undo list.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fdo_005fundo"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_do_undo</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fdo_005fundo"> &para;</a></span></dt>
<dd><p>Undo the first thing on the undo list.
Returns <code class="code">0</code> if there was nothing to undo,
non-zero if something was undone.
</p></dd></dl>

<p>Finally, if you neither insert nor delete text, but directly modify the
existing text (e.g., change its case), call <code class="code">rl_modifying()</code>
once, just before you modify the text.
You must supply the indices of the text range that you are going to modify.
Readline will create an undo group for you.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fmodifying"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_modifying</strong> <code class="def-code-arguments">(int start, int end)</code><a class="copiable-link" href="#index-rl_005fmodifying"> &para;</a></span></dt>
<dd><p>Tell Readline to save the text between <var class="var">start</var> and <var class="var">end</var> as a
single undo unit.
It is assumed that you will subsequently modify that text.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Redisplay">
<div class="nav-panel">
<p>
Next: <a href="#Modifying-Text" accesskey="n" rel="next">Modifying Text</a>, Previous: <a href="#Allowing-Undoing" accesskey="p" rel="prev">Allowing Undoing</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Redisplay-1"><span>2.4.6 Redisplay<a class="copiable-link" href="#Redisplay-1"> &para;</a></span></h4>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fredisplay"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_redisplay</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fredisplay"> &para;</a></span></dt>
<dd><p>Change what&rsquo;s displayed on the screen to reflect the current contents
of <code class="code">rl_line_buffer</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fforced_005fupdate_005fdisplay"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_forced_update_display</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fforced_005fupdate_005fdisplay"> &para;</a></span></dt>
<dd><p>Force the line to be updated and redisplayed, whether or not
Readline thinks the screen display is correct.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fon_005fnew_005fline"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_on_new_line</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fon_005fnew_005fline"> &para;</a></span></dt>
<dd><p>Tell the update functions that we have moved onto a new (empty) line,
usually after outputting a newline.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fon_005fnew_005fline_005fwith_005fprompt"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_on_new_line_with_prompt</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fon_005fnew_005fline_005fwith_005fprompt"> &para;</a></span></dt>
<dd><p>Tell the update functions that we have moved onto a new line, with
<var class="var">rl_prompt</var> already displayed.
This could be used by applications that want to output the prompt string
themselves, but still need Readline to know the prompt string length for
redisplay.
It should be used after setting <var class="var">rl_already_prompted</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fclear_005fvisible_005fline"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_clear_visible_line</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fclear_005fvisible_005fline"> &para;</a></span></dt>
<dd><p>Clear the screen lines corresponding to the current line&rsquo;s contents.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005freset_005fline_005fstate"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_reset_line_state</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005freset_005fline_005fstate"> &para;</a></span></dt>
<dd><p>Reset the display state to a clean state and redisplay the current line
starting on a new line.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcrlf"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_crlf</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fcrlf"> &para;</a></span></dt>
<dd><p>Move the cursor to the start of the next screen line.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fshow_005fchar"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_show_char</strong> <code class="def-code-arguments">(int c)</code><a class="copiable-link" href="#index-rl_005fshow_005fchar"> &para;</a></span></dt>
<dd><p>Display character <var class="var">c</var> on <code class="code">rl_outstream</code>.
If Readline has not been set to display meta characters directly, this
will convert meta characters to a meta-prefixed key sequence.
This is intended for use by applications which wish to do their own
redisplay.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fmessage"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_message</strong> <code class="def-code-arguments">(const char *, &hellip;)</code><a class="copiable-link" href="#index-rl_005fmessage"> &para;</a></span></dt>
<dd><p>The arguments are a format string as would be supplied to <code class="code">printf</code>,
possibly containing conversion specifications such as &lsquo;<samp class="samp">%d</samp>&rsquo;, and
any additional arguments necessary to satisfy the conversion specifications.
The resulting string is displayed in the <em class="dfn">echo area</em>.
The echo area is also used to display numeric arguments and search strings.
You should call <code class="code">rl_save_prompt</code> to save the prompt information
before calling this function.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fclear_005fmessage"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_clear_message</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fclear_005fmessage"> &para;</a></span></dt>
<dd><p>Clear the message in the echo area.
If the prompt was saved with a call to
<code class="code">rl_save_prompt</code> before the last call to <code class="code">rl_message</code>,
you must call <code class="code">rl_restore_prompt</code> before calling this function.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fsave_005fprompt"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_save_prompt</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fsave_005fprompt"> &para;</a></span></dt>
<dd><p>Save the local Readline prompt display state in preparation for
displaying a new message in the message area with <code class="code">rl_message()</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005frestore_005fprompt"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_restore_prompt</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005frestore_005fprompt"> &para;</a></span></dt>
<dd><p>Restore the local Readline prompt display state saved by the most
recent call to <code class="code">rl_save_prompt</code>.
If you called <code class="code">rl_save_prompt</code> to save the prompt before a call
to <code class="code">rl_message</code>, you should call this function before the
corresponding call to <code class="code">rl_clear_message</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fexpand_005fprompt"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_expand_prompt</strong> <code class="def-code-arguments">(char *prompt)</code><a class="copiable-link" href="#index-rl_005fexpand_005fprompt"> &para;</a></span></dt>
<dd><p>Expand any special character sequences in <var class="var">prompt</var> and set up the
local Readline prompt redisplay variables.
This function is called by <code class="code">readline()</code>.
It may also be called to
expand the primary prompt if the application uses the
<code class="code">rl_on_new_line_with_prompt()</code> function or
<code class="code">rl_already_prompted</code> variable.
It returns the number of visible characters on the last line of the
(possibly multi-line) prompt.
Applications may indicate that the prompt contains characters that take
up no physical screen space when displayed by bracketing a sequence of
such characters with the special markers <code class="code">RL_PROMPT_START_IGNORE</code>
and <code class="code">RL_PROMPT_END_IGNORE</code> (declared in <samp class="file">readline.h</samp> as
&lsquo;<samp class="samp">\001</samp>&rsquo; and &lsquo;<samp class="samp">\002</samp>&rsquo;, respectively).
This may be used to embed terminal-specific escape sequences in prompts.
If you don&rsquo;t use these indicators, redisplay will likely produce screen
contents that don&rsquo;t match the line buffer.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fset_005fprompt"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_set_prompt</strong> <code class="def-code-arguments">(const char *prompt)</code><a class="copiable-link" href="#index-rl_005fset_005fprompt"> &para;</a></span></dt>
<dd><p>Make Readline use <var class="var">prompt</var> for subsequent redisplay.
This calls <code class="code">rl_expand_prompt()</code> to expand the prompt
and sets <code class="code">rl_prompt</code> to the result.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Modifying-Text">
<div class="nav-panel">
<p>
Next: <a href="#Character-Input" accesskey="n" rel="next">Character Input</a>, Previous: <a href="#Redisplay" accesskey="p" rel="prev">Redisplay</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Modifying-Text-1"><span>2.4.7 Modifying Text<a class="copiable-link" href="#Modifying-Text-1"> &para;</a></span></h4>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005finsert_005ftext"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_insert_text</strong> <code class="def-code-arguments">(const char *text)</code><a class="copiable-link" href="#index-rl_005finsert_005ftext"> &para;</a></span></dt>
<dd><p>Insert <var class="var">text</var> into the line at the current cursor position.
Returns the number of characters inserted.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fdelete_005ftext"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_delete_text</strong> <code class="def-code-arguments">(int start, int end)</code><a class="copiable-link" href="#index-rl_005fdelete_005ftext"> &para;</a></span></dt>
<dd><p>Delete the text between <var class="var">start</var> and <var class="var">end</var> in the current line.
Returns the number of characters deleted.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcopy_005ftext"><span class="category-def">Function: </span><span><code class="def-type">char *</code> <strong class="def-name">rl_copy_text</strong> <code class="def-code-arguments">(int start, int end)</code><a class="copiable-link" href="#index-rl_005fcopy_005ftext"> &para;</a></span></dt>
<dd><p>Return a copy of the text between <var class="var">start</var> and <var class="var">end</var> in
the current line.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fkill_005ftext"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_kill_text</strong> <code class="def-code-arguments">(int start, int end)</code><a class="copiable-link" href="#index-rl_005fkill_005ftext"> &para;</a></span></dt>
<dd><p>Copy the text between <var class="var">start</var> and <var class="var">end</var> in the current line
to the kill ring, appending or prepending to the last kill if the
last command was a kill command.
This deletes the text from the line.
If <var class="var">start</var> is less than <var class="var">end</var>, the text is appended,
otherwise it is prepended.
If the last command was not a kill, this uses a new kill ring slot.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005freplace_005fline"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_replace_line</strong> <code class="def-code-arguments">(const char *text, int clear_undo)</code><a class="copiable-link" href="#index-rl_005freplace_005fline"> &para;</a></span></dt>
<dd><p>Replace the contents of <code class="code">rl_line_buffer</code> with <var class="var">text</var>.
This preserves the point and mark, if possible.
If <var class="var">clear_undo</var> is non-zero, this clears the undo list associated
with the current line.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fpush_005fmacro_005finput"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_push_macro_input</strong> <code class="def-code-arguments">(char *macro)</code><a class="copiable-link" href="#index-rl_005fpush_005fmacro_005finput"> &para;</a></span></dt>
<dd><p>Insert <var class="var">macro</var> into the line, as if it had been invoked
by a key bound to a macro.
Not especially useful; use <code class="code">rl_insert_text()</code> instead.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Character-Input">
<div class="nav-panel">
<p>
Next: <a href="#Terminal-Management" accesskey="n" rel="next">Terminal Management</a>, Previous: <a href="#Modifying-Text" accesskey="p" rel="prev">Modifying Text</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Character-Input-1"><span>2.4.8 Character Input<a class="copiable-link" href="#Character-Input-1"> &para;</a></span></h4>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fread_005fkey"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_read_key</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fread_005fkey"> &para;</a></span></dt>
<dd><p>Return the next character available from Readline&rsquo;s current input stream.
This handles input inserted into
the input stream via <var class="var">rl_pending_input</var> (see <a class="pxref" href="#Readline-Variables">Readline Variables</a>)
and <code class="code">rl_stuff_char()</code>, macros, and characters read from the keyboard.
While waiting for input, this function will call any function assigned to
the <code class="code">rl_event_hook</code> variable.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fgetc"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_getc</strong> <code class="def-code-arguments">(FILE *stream)</code><a class="copiable-link" href="#index-rl_005fgetc"> &para;</a></span></dt>
<dd><p>Return the next character available from <var class="var">stream</var>, which is assumed to
be the keyboard.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fstuff_005fchar"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_stuff_char</strong> <code class="def-code-arguments">(int c)</code><a class="copiable-link" href="#index-rl_005fstuff_005fchar"> &para;</a></span></dt>
<dd><p>Insert <var class="var">c</var> into the Readline input stream.
It will be &quot;read&quot; before Readline attempts to read characters
from the terminal with <code class="code">rl_read_key()</code>.
Applications can push back up to 512 characters.
<code class="code">rl_stuff_char</code> returns 1 if the character was successfully inserted;
0 otherwise.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fexecute_005fnext"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_execute_next</strong> <code class="def-code-arguments">(int c)</code><a class="copiable-link" href="#index-rl_005fexecute_005fnext"> &para;</a></span></dt>
<dd><p>Make <var class="var">c</var> be the next command to be executed when <code class="code">rl_read_key()</code>
is called.
This sets <var class="var">rl_pending_input</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fclear_005fpending_005finput"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_clear_pending_input</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fclear_005fpending_005finput"> &para;</a></span></dt>
<dd><p>Unset <var class="var">rl_pending_input</var>, effectively negating the effect of any
previous call to <code class="code">rl_execute_next()</code>.
This works only if the pending input has not already been read
with <code class="code">rl_read_key()</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fset_005fkeyboard_005finput_005ftimeout"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_set_keyboard_input_timeout</strong> <code class="def-code-arguments">(int u)</code><a class="copiable-link" href="#index-rl_005fset_005fkeyboard_005finput_005ftimeout"> &para;</a></span></dt>
<dd><p>While waiting for keyboard input in <code class="code">rl_read_key()</code>, Readline will
wait for <var class="var">u</var> microseconds for input before calling any function
assigned to <code class="code">rl_event_hook</code>.
<var class="var">u</var> must be greater than or equal
to zero (a zero-length timeout is equivalent to a poll).
The default waiting period is one-tenth of a second.
Returns the old timeout value.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fset_005ftimeout"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_set_timeout</strong> <code class="def-code-arguments">(unsigned int secs, unsigned int usecs)</code><a class="copiable-link" href="#index-rl_005fset_005ftimeout"> &para;</a></span></dt>
<dd><p>Set a timeout for subsequent calls to <code class="code">readline()</code>.
If Readline does not read a complete line, or the number of characters
specified by <code class="code">rl_num_chars_to_read</code>,
before the duration specified by <var class="var">secs</var> (in seconds)
and <var class="var">usecs</var> (microseconds), it returns and sets
<code class="code">RL_STATE_TIMEOUT</code> in <code class="code">rl_readline_state</code>.
Passing 0 for <code class="code">secs</code> and <code class="code">usecs</code> cancels any previously set
timeout; the convenience macro <code class="code">rl_clear_timeout()</code> is shorthand
for this.
Returns 0 if the timeout is set successfully.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ftimeout_005fremaining"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_timeout_remaining</strong> <code class="def-code-arguments">(unsigned int *secs, unsigned int *usecs)</code><a class="copiable-link" href="#index-rl_005ftimeout_005fremaining"> &para;</a></span></dt>
<dd><p>Return the number of seconds and microseconds remaining in the current
timeout duration in <var class="var">*secs</var> and <var class="var">*usecs</var>, respectively.
Both <var class="var">*secs</var> and <var class="var">*usecs</var> must be non-NULL to return any values.
The return value is -1 on error or when there is no timeout set,
0 when the timeout has expired (leaving <var class="var">*secs</var> and <var class="var">*usecs</var>
unchanged),
and 1 if the timeout has not expired.
If either of <var class="var">secs</var> and <var class="var">usecs</var> is <code class="code">NULL</code>,
the return value indicates whether the timeout has expired.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Terminal-Management">
<div class="nav-panel">
<p>
Next: <a href="#Utility-Functions" accesskey="n" rel="next">Utility Functions</a>, Previous: <a href="#Character-Input" accesskey="p" rel="prev">Character Input</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Terminal-Management-1"><span>2.4.9 Terminal Management<a class="copiable-link" href="#Terminal-Management-1"> &para;</a></span></h4>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fprep_005fterminal"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_prep_terminal</strong> <code class="def-code-arguments">(int meta_flag)</code><a class="copiable-link" href="#index-rl_005fprep_005fterminal"> &para;</a></span></dt>
<dd><p>Modify the terminal settings for Readline&rsquo;s use, so <code class="code">readline()</code>
can read a single character at a time from the keyboard
and perform redisplay.
The <var class="var">meta_flag</var> argument should be non-zero if Readline should
read eight-bit input.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fdeprep_005fterminal"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_deprep_terminal</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fdeprep_005fterminal"> &para;</a></span></dt>
<dd><p>Undo the effects of <code class="code">rl_prep_terminal()</code>, leaving the terminal in
the state in which it was before the most recent call to
<code class="code">rl_prep_terminal()</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ftty_005fset_005fdefault_005fbindings"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_tty_set_default_bindings</strong> <code class="def-code-arguments">(Keymap kmap)</code><a class="copiable-link" href="#index-rl_005ftty_005fset_005fdefault_005fbindings"> &para;</a></span></dt>
<dd><p>Read the operating system&rsquo;s terminal editing characters (as would be
displayed by <code class="code">stty</code>) to their Readline equivalents.
The bindings are performed in <var class="var">kmap</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ftty_005funset_005fdefault_005fbindings"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_tty_unset_default_bindings</strong> <code class="def-code-arguments">(Keymap kmap)</code><a class="copiable-link" href="#index-rl_005ftty_005funset_005fdefault_005fbindings"> &para;</a></span></dt>
<dd><p>Reset the bindings manipulated by <code class="code">rl_tty_set_default_bindings</code> so
that the terminal editing characters are bound to <code class="code">rl_insert</code>.
The bindings are performed in <var class="var">kmap</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ftty_005fset_005fechoing"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_tty_set_echoing</strong> <code class="def-code-arguments">(int value)</code><a class="copiable-link" href="#index-rl_005ftty_005fset_005fechoing"> &para;</a></span></dt>
<dd><p>Set Readline&rsquo;s idea of whether or not it is
echoing output to its output stream (<var class="var">rl_outstream</var>).
If <var class="var">value</var> is 0,
Readline does not display output to <var class="var">rl_outstream</var>; any other
value enables output.
The initial value is set when Readline initializes the terminal settings.
This function returns the previous value.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005freset_005fterminal"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_reset_terminal</strong> <code class="def-code-arguments">(const char *terminal_name)</code><a class="copiable-link" href="#index-rl_005freset_005fterminal"> &para;</a></span></dt>
<dd><p>Reinitialize Readline&rsquo;s idea of the terminal settings using
<var class="var">terminal_name</var> as the terminal type (e.g., <code class="code">xterm</code>).
If <var class="var">terminal_name</var> is <code class="code">NULL</code>, Readline uses the value of the
<code class="code">TERM</code> environment variable.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Utility-Functions">
<div class="nav-panel">
<p>
Next: <a href="#Miscellaneous-Functions" accesskey="n" rel="next">Miscellaneous Functions</a>, Previous: <a href="#Terminal-Management" accesskey="p" rel="prev">Terminal Management</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Utility-Functions-1"><span>2.4.10 Utility Functions<a class="copiable-link" href="#Utility-Functions-1"> &para;</a></span></h4>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fsave_005fstate"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_save_state</strong> <code class="def-code-arguments">(struct readline_state *sp)</code><a class="copiable-link" href="#index-rl_005fsave_005fstate"> &para;</a></span></dt>
<dd><p>Save a snapshot of Readline&rsquo;s internal state to <var class="var">sp</var>.
The contents of the <var class="var">readline_state</var> structure are
documented in <samp class="file">readline.h</samp>.
The caller is responsible for allocating the structure.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005frestore_005fstate"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_restore_state</strong> <code class="def-code-arguments">(struct readline_state *sp)</code><a class="copiable-link" href="#index-rl_005frestore_005fstate"> &para;</a></span></dt>
<dd><p>Restore Readline&rsquo;s internal state to that stored in <var class="var">sp</var>,
which must have been saved by a call to <code class="code">rl_save_state</code>. 
The contents of the <var class="var">readline_state</var> structure are documented in
<samp class="file">readline.h</samp>.
The caller is responsible for freeing the structure. 
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ffree"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_free</strong> <code class="def-code-arguments">(void *mem)</code><a class="copiable-link" href="#index-rl_005ffree"> &para;</a></span></dt>
<dd><p>Deallocate the memory pointed to by <var class="var">mem</var>.
<var class="var">mem</var> must have been allocated by <code class="code">malloc</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fextend_005fline_005fbuffer"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_extend_line_buffer</strong> <code class="def-code-arguments">(int len)</code><a class="copiable-link" href="#index-rl_005fextend_005fline_005fbuffer"> &para;</a></span></dt>
<dd><p>Ensure that <code class="code">rl_line_buffer</code> has enough space to hold <var class="var">len</var>
characters, reallocating it if necessary. 
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005finitialize"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_initialize</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005finitialize"> &para;</a></span></dt>
<dd><p>Initialize or re-initialize Readline&rsquo;s internal state.
It&rsquo;s not strictly necessary to call this;
<code class="code">readline()</code> calls it before reading any input.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fding"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_ding</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fding"> &para;</a></span></dt>
<dd><p>Ring the terminal bell, obeying the setting of <code class="code">bell-style</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005falphabetic"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_alphabetic</strong> <code class="def-code-arguments">(int c)</code><a class="copiable-link" href="#index-rl_005falphabetic"> &para;</a></span></dt>
<dd><p>Return 1 if <var class="var">c</var> is an alphabetic character.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fdisplay_005fmatch_005flist"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_display_match_list</strong> <code class="def-code-arguments">(char **matches, int len, int max)</code><a class="copiable-link" href="#index-rl_005fdisplay_005fmatch_005flist"> &para;</a></span></dt>
<dd><p>A convenience function for displaying a list of strings in
columnar format on Readline&rsquo;s output stream.
<code class="code">matches</code> is the list of strings, in argv format,
such as a list of completion matches.
<code class="code">len</code> is the number of strings in <code class="code">matches</code>, and <code class="code">max</code>
is the length of the longest string in <code class="code">matches</code>.
This function uses the setting of <code class="code">print-completions-horizontally</code>
to select how the matches are displayed (see <a class="pxref" href="#Readline-Init-File-Syntax">Readline Init File Syntax</a>).
When displaying completions, this function sets the number of columns used
for display to the value of <code class="code">completion-display-width</code>, the value of
the environment variable <code class="env">COLUMNS</code>, or the screen width, in that order.
</p></dd></dl>

<p>The following are implemented as macros, defined in <code class="code">chardefs.h</code>.
Applications should refrain from using them.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-_005frl_005fuppercase_005fp"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">_rl_uppercase_p</strong> <code class="def-code-arguments">(int c)</code><a class="copiable-link" href="#index-_005frl_005fuppercase_005fp"> &para;</a></span></dt>
<dd><p>Return 1 if <var class="var">c</var> is an uppercase alphabetic character.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-_005frl_005flowercase_005fp"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">_rl_lowercase_p</strong> <code class="def-code-arguments">(int c)</code><a class="copiable-link" href="#index-_005frl_005flowercase_005fp"> &para;</a></span></dt>
<dd><p>Return 1 if <var class="var">c</var> is a lowercase alphabetic character.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-_005frl_005fdigit_005fp"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">_rl_digit_p</strong> <code class="def-code-arguments">(int c)</code><a class="copiable-link" href="#index-_005frl_005fdigit_005fp"> &para;</a></span></dt>
<dd><p>Return 1 if <var class="var">c</var> is a numeric character.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-_005frl_005fto_005fupper"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">_rl_to_upper</strong> <code class="def-code-arguments">(int c)</code><a class="copiable-link" href="#index-_005frl_005fto_005fupper"> &para;</a></span></dt>
<dd><p>If <var class="var">c</var> is a lowercase alphabetic character, return the corresponding
uppercase character.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-_005frl_005fto_005flower"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">_rl_to_lower</strong> <code class="def-code-arguments">(int c)</code><a class="copiable-link" href="#index-_005frl_005fto_005flower"> &para;</a></span></dt>
<dd><p>If <var class="var">c</var> is an uppercase alphabetic character, return the corresponding
lowercase character.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-_005frl_005fdigit_005fvalue"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">_rl_digit_value</strong> <code class="def-code-arguments">(int c)</code><a class="copiable-link" href="#index-_005frl_005fdigit_005fvalue"> &para;</a></span></dt>
<dd><p>If <var class="var">c</var> is a number, return the value it represents.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Miscellaneous-Functions">
<div class="nav-panel">
<p>
Next: <a href="#Alternate-Interface" accesskey="n" rel="next">Alternate Interface</a>, Previous: <a href="#Utility-Functions" accesskey="p" rel="prev">Utility Functions</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Miscellaneous-Functions-1"><span>2.4.11 Miscellaneous Functions<a class="copiable-link" href="#Miscellaneous-Functions-1"> &para;</a></span></h4>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fmacro_005fbind"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_macro_bind</strong> <code class="def-code-arguments">(const char *keyseq, const char *macro, Keymap map)</code><a class="copiable-link" href="#index-rl_005fmacro_005fbind"> &para;</a></span></dt>
<dd><p>Bind the key sequence <var class="var">keyseq</var> to invoke the macro <var class="var">macro</var>.
The binding is performed in <var class="var">map</var>.
When <var class="var">keyseq</var> is invoked, the <var class="var">macro</var> will be inserted into the line.
This function is deprecated; use <code class="code">rl_generic_bind</code> instead.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fmacro_005fdumper"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_macro_dumper</strong> <code class="def-code-arguments">(int readable)</code><a class="copiable-link" href="#index-rl_005fmacro_005fdumper"> &para;</a></span></dt>
<dd><p>Print the key sequences bound to macros and their values, using
the current keymap, to <code class="code">rl_outstream</code>.
If the application has assigned a value to <code class="code">rl_macro_display_hook</code>,
<code class="code">rl_macro_dumper</code> calls it instead of printing anything.
If <var class="var">readable</var> is greater than zero, the list is formatted in such a way
that it can be made part of an <code class="code">inputrc</code> file and re-read.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fvariable_005fbind"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_variable_bind</strong> <code class="def-code-arguments">(const char *variable, const char *value)</code><a class="copiable-link" href="#index-rl_005fvariable_005fbind"> &para;</a></span></dt>
<dd><p>Make the Readline variable <var class="var">variable</var> have <var class="var">value</var>.
This behaves as if the Readline command
&lsquo;<samp class="samp">set <var class="var">variable</var> <var class="var">value</var></samp>&rsquo; had been executed in an <code class="code">inputrc</code>
file (see <a class="pxref" href="#Readline-Init-File-Syntax">Readline Init File Syntax</a>)
or by <code class="code">rl_parse_and_bind</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fvariable_005fvalue"><span class="category-def">Function: </span><span><code class="def-type">char *</code> <strong class="def-name">rl_variable_value</strong> <code class="def-code-arguments">(const char *variable)</code><a class="copiable-link" href="#index-rl_005fvariable_005fvalue"> &para;</a></span></dt>
<dd><p>Return a string representing the value of the Readline variable <var class="var">variable</var>.
For boolean variables, this string is either &lsquo;<samp class="samp">on</samp>&rsquo; or &lsquo;<samp class="samp">off</samp>&rsquo;.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fvariable_005fdumper"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_variable_dumper</strong> <code class="def-code-arguments">(int readable)</code><a class="copiable-link" href="#index-rl_005fvariable_005fdumper"> &para;</a></span></dt>
<dd><p>Print the Readline variable names and their current values
to <code class="code">rl_outstream</code>.
If <var class="var">readable</var> is non-zero, the list is formatted in such a way
that it can be made part of an <code class="code">inputrc</code> file and re-read.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fset_005fparen_005fblink_005ftimeout"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_set_paren_blink_timeout</strong> <code class="def-code-arguments">(int u)</code><a class="copiable-link" href="#index-rl_005fset_005fparen_005fblink_005ftimeout"> &para;</a></span></dt>
<dd><p>Set the time interval (in microseconds) that Readline waits when showing
a balancing character when <code class="code">blink-matching-paren</code> has been enabled.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fget_005ftermcap"><span class="category-def">Function: </span><span><code class="def-type">char *</code> <strong class="def-name">rl_get_termcap</strong> <code class="def-code-arguments">(const char *cap)</code><a class="copiable-link" href="#index-rl_005fget_005ftermcap"> &para;</a></span></dt>
<dd><p>Retrieve the string value of the termcap capability <var class="var">cap</var>.
Readline fetches the termcap entry for the current terminal name and
uses those capabilities to move around the screen line and perform other
terminal-specific operations, like erasing a line.
Readline does not fetch or use all of a terminal&rsquo;s capabilities,
and this function will return
values for only those capabilities Readline fetches.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005freparse_005fcolors"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_reparse_colors</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005freparse_005fcolors"> &para;</a></span></dt>
<dd><p>Read or re-read color definitions from <code class="env">LS_COLORS</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fclear_005fhistory"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_clear_history</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fclear_005fhistory"> &para;</a></span></dt>
<dd><p>Clear the history list by deleting all of the entries, in the same manner
as the History library&rsquo;s <code class="code">clear_history()</code> function.
This differs from <code class="code">clear_history</code> because it frees private data
Readline saves in the history list.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005factivate_005fmark"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_activate_mark</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005factivate_005fmark"> &para;</a></span></dt>
<dd><p>Enable an <em class="emph">active</em> region.
When this is enabled, the text between point and mark (the <var class="var">region</var>) is
displayed using the color specified by the value of the
<code class="code">active-region-start-color</code> variable (a <var class="var">face</var>).
The default face is the terminal&rsquo;s standout mode.
This is called by various Readline functions that set the mark and insert
text, and is available for applications to call.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fdeactivate_005fmark"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_deactivate_mark</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fdeactivate_005fmark"> &para;</a></span></dt>
<dd><p>Turn off the active region.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fkeep_005fmark_005factive"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_keep_mark_active</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fkeep_005fmark_005factive"> &para;</a></span></dt>
<dd><p>Indicate that the mark should remain active when the current Readline
function completes and after redisplay occurs.
In most cases, the mark remains active for only the duration of a single
bindable Readline function.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fmark_005factive_005fp"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_mark_active_p</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fmark_005factive_005fp"> &para;</a></span></dt>
<dd><p>Return a non-zero value if the mark is currently active; zero otherwise.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Alternate-Interface">
<div class="nav-panel">
<p>
Next: <a href="#A-Readline-Example" accesskey="n" rel="next">A Readline Example</a>, Previous: <a href="#Miscellaneous-Functions" accesskey="p" rel="prev">Miscellaneous Functions</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Alternate-Interface-1"><span>2.4.12 Alternate Interface<a class="copiable-link" href="#Alternate-Interface-1"> &para;</a></span></h4>

<p>For applications that need more granular control than
plain <code class="code">readline()</code> provides, there is
an alternate interface.
Some applications need to interleave keyboard I/O with file, device,
or window system I/O, typically by using a main loop to <code class="code">select()</code>
on various file descriptors.
To accommodate this use case, Readline can
also be invoked as a &lsquo;callback&rsquo; function from an event loop.
There are functions available to make this easy.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcallback_005fhandler_005finstall"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_callback_handler_install</strong> <code class="def-code-arguments">(const char *prompt, rl_vcpfunc_t *line_handler)</code><a class="copiable-link" href="#index-rl_005fcallback_005fhandler_005finstall"> &para;</a></span></dt>
<dd><p>Set up the terminal for Readline I/O and display the initial
expanded value of <var class="var">prompt</var>.
Save the value of <var class="var">line_handler</var> to
use as a handler function to call when a complete line of input has been
entered.
The handler function receives the text of the line as an argument.
As with <code class="code">readline()</code>, the handler function should <code class="code">free</code> the
line when it it finished with it.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcallback_005fread_005fchar"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_callback_read_char</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fcallback_005fread_005fchar"> &para;</a></span></dt>
<dd><p>Whenever an application determines that keyboard input is available, it
should call <code class="code">rl_callback_read_char()</code>, which will read the next
character from the current input source.
If that character completes the line, <code class="code">rl_callback_read_char</code> will
invoke the <var class="var">line_handler</var> function installed by
<code class="code">rl_callback_handler_install</code> to process the line.
Before calling the <var class="var">line_handler</var> function, Readline resets
the terminal settings to the values they had before calling
<code class="code">rl_callback_handler_install</code>.
If the <var class="var">line_handler</var> function returns,
and the line handler remains installed,
Readline modifies the terminal settings for its use again.
<code class="code">EOF</code> is indicated by calling <var class="var">line_handler</var> with a
<code class="code">NULL</code> line.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcallback_005fsigcleanup"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_callback_sigcleanup</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fcallback_005fsigcleanup"> &para;</a></span></dt>
<dd><p>Clean up any internal state the callback interface uses to maintain state
between calls to rl_callback_read_char (e.g., the state of any active
incremental searches).
This is intended to be used by applications that
wish to perform their own signal handling;
Readline&rsquo;s internal signal handler calls this when appropriate.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcallback_005fhandler_005fremove"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_callback_handler_remove</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fcallback_005fhandler_005fremove"> &para;</a></span></dt>
<dd><p>Restore the terminal to its initial state and remove the line handler.
You may call this function from within a callback as well as independently.
If the <var class="var">line_handler</var> installed by <code class="code">rl_callback_handler_install</code>
does not exit the program, your program should call
either this function or the function referred
to by the value of <code class="code">rl_deprep_term_function</code>
before the program exits to reset the terminal settings.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="A-Readline-Example">
<div class="nav-panel">
<p>
Next: <a href="#Alternate-Interface-Example" accesskey="n" rel="next">Alternate Interface Example</a>, Previous: <a href="#Alternate-Interface" accesskey="p" rel="prev">Alternate Interface</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="A-Readline-Example-1"><span>2.4.13 A Readline Example<a class="copiable-link" href="#A-Readline-Example-1"> &para;</a></span></h4>

<p>Here is a function which changes lowercase characters to their uppercase
equivalents, and uppercase characters to lowercase.
If this function was bound to &lsquo;<samp class="samp">M-c</samp>&rsquo;, then typing &lsquo;<samp class="samp">M-c</samp>&rsquo; would
change the case of the character under point.
Typing &lsquo;<samp class="samp">M-1 0 M-c</samp>&rsquo; would change the case
of the following 10 characters, leaving the cursor on
the last character changed.
</p>
<div class="example">
<pre class="example-preformatted">/* Invert the case of the COUNT following characters. */
int
invert_case_line (count, key)
     int count, key;
{
  int start, end, i;

  start = rl_point;

  if (rl_point &gt;= rl_end)
    return (0);

  /* Find the end of the range to modify. */
  end = start + count;

  /* Force it to be within range. */
  if (end &gt; rl_end)
    end = rl_end;
  else if (end &lt; 0)
    end = 0;

  if (start == end)
    return (0);

  /* For positive arguments, put point after the last changed character. For
     negative arguments, put point before the last changed character. */
  rl_point = end;

  /* Swap start and end if we are moving backwards */
  if (start &gt; end)
    {
      int temp = start;
      start = end;
      end = temp;
    }

  /* Tell readline that we are modifying the line,
     so it will save the undo information. */
  rl_modifying (start, end);

  for (i = start; i != end; i++)
    {
      if (_rl_uppercase_p (rl_line_buffer[i]))
        rl_line_buffer[i] = _rl_to_lower (rl_line_buffer[i]);
      else if (_rl_lowercase_p (rl_line_buffer[i]))
        rl_line_buffer[i] = _rl_to_upper (rl_line_buffer[i]);
    }

  return (0);
}
</pre></div>

<hr>
</div>
<div class="subsection-level-extent" id="Alternate-Interface-Example">
<div class="nav-panel">
<p>
Previous: <a href="#A-Readline-Example" accesskey="p" rel="prev">A Readline Example</a>, Up: <a href="#Readline-Convenience-Functions" accesskey="u" rel="up">Readline Convenience Functions</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Alternate-Interface-Example-1"><span>2.4.14 Alternate Interface Example<a class="copiable-link" href="#Alternate-Interface-Example-1"> &para;</a></span></h4>

<p>Here is a complete program that illustrates Readline&rsquo;s alternate interface.
It reads lines from the terminal and displays them, providing the
standard history and TAB completion functions.
It understands the EOF character or &quot;exit&quot; to exit the program.
</p>
<div class="example">
<pre class="example-preformatted">/* Standard include files. stdio.h is required. */
#include &lt;stdlib.h&gt;
#include &lt;string.h&gt;
#include &lt;unistd.h&gt;

/* Used for select(2) */
#include &lt;sys/types.h&gt;
#include &lt;sys/select.h&gt;

#include &lt;signal.h&gt;

#include &lt;errno.h&gt;
#include &lt;stdio.h&gt;

#include &lt;locale.h&gt;

/* Standard readline include files. */
#include &lt;readline/readline.h&gt;
#include &lt;readline/history.h&gt;

#if !defined (errno)
extern int errno;
#endif

static void cb_linehandler (char *);
static void sighandler (int);

int running;
int sigwinch_received;
const char *prompt = &quot;rltest$ &quot;;

/* Handle SIGWINCH and window size changes when readline is not active and
   reading a character. */
static void
sighandler (int sig)
{
  sigwinch_received = 1;
}

/* Callback function called for each line when accept-line executed, EOF
   seen, or EOF character read.  This sets a flag and returns; it could
   also call exit(3). */
static void
cb_linehandler (char *line)
{
  /* Can use ^D (stty eof) or `exit' to exit. */
  if (line == NULL || strcmp (line, &quot;exit&quot;) == 0)
    {
      if (line == 0)
        printf (&quot;\n&quot;);
      printf (&quot;exit\n&quot;);
      /* This function needs to be called to reset the terminal settings,
         and calling it from the line handler keeps one extra prompt from
         being displayed. */
      rl_callback_handler_remove ();

      running = 0;
    }
  else
    {
      if (*line)
        add_history (line);
      printf (&quot;input line: %s\n&quot;, line);
      free (line);
    }
}

int
main (int c, char **v)
{
  fd_set fds;
  int r;

  /* Set the default locale values according to environment variables. */
  setlocale (LC_ALL, &quot;&quot;);

  /* Handle window size changes when readline is not active and reading
     characters. */
  signal (SIGWINCH, sighandler);

  /* Install the line handler. */
  rl_callback_handler_install (prompt, cb_linehandler);

  /* Enter a simple event loop.  This waits until something is available
     to read on readline's input stream (defaults to standard input) and
     calls the builtin character read callback to read it.  It does not
     have to modify the user's terminal settings. */
  running = 1;
  while (running)
    {
      FD_ZERO (&amp;fds);
      FD_SET (fileno (rl_instream), &amp;fds);

      r = select (FD_SETSIZE, &amp;fds, NULL, NULL, NULL);
      if (r &lt; 0 &amp;&amp; errno != EINTR)
        {
          perror (&quot;rltest: select&quot;);
          rl_callback_handler_remove ();
          break;
        }
      if (sigwinch_received)
	{
	  rl_resize_terminal ();
	  sigwinch_received = 0;
	}
      if (r &lt; 0)
	continue;     

      if (FD_ISSET (fileno (rl_instream), &amp;fds))
        rl_callback_read_char ();
    }

  printf (&quot;rltest: Event loop has exited\n&quot;);
  return 0;
}
</pre></div>

<hr>
</div>
</div>
<div class="section-level-extent" id="Readline-Signal-Handling">
<div class="nav-panel">
<p>
Next: <a href="#Custom-Completers" accesskey="n" rel="next">Custom Completers</a>, Previous: <a href="#Readline-Convenience-Functions" accesskey="p" rel="prev">Readline Convenience Functions</a>, Up: <a href="#Programming-with-GNU-Readline" accesskey="u" rel="up">Programming with GNU Readline</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h3 class="section" id="Readline-Signal-Handling-1"><span>2.5 Readline Signal Handling<a class="copiable-link" href="#Readline-Signal-Handling-1"> &para;</a></span></h3>

<p>Signals are asynchronous events sent to a process by the Unix kernel,
sometimes on behalf of another process.
They are intended to indicate exceptional events,
like a user pressing the terminal&rsquo;s interrupt key,
or a network connection being broken.
There is a class of signals that can
be sent to the process currently reading input from the keyboard.
Since Readline changes the terminal attributes when it is called, it needs
to perform special processing when such a signal is received in order to
restore the terminal to a sane state, or provide applications using
Readline with functions to do so manually. 
</p>
<p>Readline contains an internal signal handler that is installed for a
number of signals (<code class="code">SIGINT</code>, <code class="code">SIGQUIT</code>, <code class="code">SIGTERM</code>,
<code class="code">SIGHUP</code>, 
<code class="code">SIGALRM</code>, <code class="code">SIGTSTP</code>, <code class="code">SIGTTIN</code>, and <code class="code">SIGTTOU</code>).
When Readline receives one of these signals, the signal handler
will reset the terminal attributes to those that were in effect before
<code class="code">readline()</code> was called, reset the signal handling to what it was
before <code class="code">readline()</code> was called, and resend the signal to the calling
application.
If and when the calling application&rsquo;s signal handler returns, Readline
will reinitialize the terminal and continue to accept input.
When a <code class="code">SIGINT</code> is received, the Readline signal handler performs
some additional work, which will cause any partially-entered line to be
aborted (see the description of <code class="code">rl_free_line_state()</code> below).
</p>
<p>There is an additional Readline signal handler, for <code class="code">SIGWINCH</code>, which
the kernel sends to a process whenever the terminal&rsquo;s size changes (for
example, if a user resizes an <code class="code">xterm</code>).
The Readline <code class="code">SIGWINCH</code> handler updates
Readline&rsquo;s internal screen size information, and then calls any
<code class="code">SIGWINCH</code> signal handler the calling application has installed. 
Readline calls the application&rsquo;s <code class="code">SIGWINCH</code> signal handler without
resetting the terminal to its original state.
If the application&rsquo;s signal
handler does more than update its idea of the terminal size and return
(for example, a <code class="code">longjmp</code> back to a main processing loop),
it <em class="emph">must</em> call <code class="code">rl_cleanup_after_signal()</code> (described below),
to restore the terminal state.
</p>
<p>When an application is using the callback interface
(see <a class="pxref" href="#Alternate-Interface">Alternate Interface</a>), Readline installs signal handlers only for
the duration of the call to <code class="code">rl_callback_read_char</code>.
Applications using the callback interface should be prepared
to clean up Readline&rsquo;s state if they wish to handle the signal
before the line handler completes and restores the terminal state.
</p>
<p>If an application using the callback interface wishes to have Readline
install its signal handlers at the time the application calls
<code class="code">rl_callback_handler_install</code> and remove them only when a complete
line of input has been read, it should set the
<code class="code">rl_persistent_signal_handlers</code> variable to a non-zero value.
This allows an application to defer all of the handling of the signals
Readline catches to Readline.
Applications should use this variable with care; it can result in Readline
catching signals and not acting on them (or allowing the application to react
to them) until the application calls <code class="code">rl_callback_read_char</code>.
This can result in an application becoming less responsive to keyboard
signals like SIGINT.
If an application does not want or need to perform any signal handling, or
does not need to do any processing
between calls to <code class="code">rl_callback_read_char</code>,
setting this variable may be appropriate.
</p>
<p>Readline provides two variables that allow application writers to
control whether or not it will catch certain signals and act on them
when they are received.
It is important that applications change the
values of these variables only when calling <code class="code">readline()</code>,
not in a signal handler, so Readline&rsquo;s internal signal state
is not corrupted.
</p>
<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcatch_005fsignals"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_catch_signals</strong><a class="copiable-link" href="#index-rl_005fcatch_005fsignals"> &para;</a></span></dt>
<dd><p>If this variable is non-zero, Readline will install signal handlers for
<code class="code">SIGINT</code>, <code class="code">SIGQUIT</code>, <code class="code">SIGTERM</code>, <code class="code">SIGHUP</code>, <code class="code">SIGALRM</code>,
<code class="code">SIGTSTP</code>, <code class="code">SIGTTIN</code>, and <code class="code">SIGTTOU</code>.
</p>
<p>The default value of <code class="code">rl_catch_signals</code> is 1.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcatch_005fsigwinch"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_catch_sigwinch</strong><a class="copiable-link" href="#index-rl_005fcatch_005fsigwinch"> &para;</a></span></dt>
<dd><p>If this variable is set to a non-zero value,
Readline will install a signal handler for <code class="code">SIGWINCH</code>.
</p>
<p>The default value of <code class="code">rl_catch_sigwinch</code> is 1.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fpersistent_005fsignal_005fhandlers"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_persistent_signal_handlers</strong><a class="copiable-link" href="#index-rl_005fpersistent_005fsignal_005fhandlers"> &para;</a></span></dt>
<dd><p>If an application using the callback interface wishes Readline&rsquo;s signal
handlers to be installed and active during the set of calls to
<code class="code">rl_callback_read_char</code> that constitutes an entire single line,
it should set this variable to a non-zero value.
</p>
<p>The default value of <code class="code">rl_persistent_signal_handlers</code> is 0.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fchange_005fenvironment"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_change_environment</strong><a class="copiable-link" href="#index-rl_005fchange_005fenvironment"> &para;</a></span></dt>
<dd><p>If this variable is set to a non-zero value,
and Readline is handling <code class="code">SIGWINCH</code>, Readline will modify the
<var class="var">LINES</var> and <var class="var">COLUMNS</var> environment variables upon receipt of a
<code class="code">SIGWINCH</code>.
</p>
<p>The default value of <code class="code">rl_change_environment</code> is 1.
</p></dd></dl>

<p>If an application does not wish to have Readline catch any signals, or
to handle signals other than those Readline catches (<code class="code">SIGHUP</code>,
for example), 
Readline provides convenience functions to do the necessary terminal
and internal state cleanup upon receipt of a signal.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fpending_005fsignal"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_pending_signal</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fpending_005fsignal"> &para;</a></span></dt>
<dd><p>Return the signal number of the most recent signal Readline received but
has not yet handled, or 0 if there is no pending signal.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcleanup_005fafter_005fsignal"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_cleanup_after_signal</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fcleanup_005fafter_005fsignal"> &para;</a></span></dt>
<dd><p>This function will reset the state of the terminal to what it was before
<code class="code">readline()</code> was called, and remove the Readline signal handlers for
all signals, depending on the values of <code class="code">rl_catch_signals</code> and
<code class="code">rl_catch_sigwinch</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ffree_005fline_005fstate"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_free_line_state</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005ffree_005fline_005fstate"> &para;</a></span></dt>
<dd><p>This will free any partial state associated with the current input line
(undo information, any partial history entry, any partially-entered
keyboard macro, and any partially-entered numeric argument).
This should be called before <code class="code">rl_cleanup_after_signal()</code>.
The Readline signal handler for <code class="code">SIGINT</code> calls this to abort
the current input line.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005freset_005fafter_005fsignal"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_reset_after_signal</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005freset_005fafter_005fsignal"> &para;</a></span></dt>
<dd><p>This will reinitialize the terminal and reinstall any Readline signal
handlers, depending on the values of <code class="code">rl_catch_signals</code> and
<code class="code">rl_catch_sigwinch</code>.
</p></dd></dl>

<p>If an application wants to force Readline to handle any signals that
have arrived while it has been executing, <code class="code">rl_check_signals()</code>
will call Readline&rsquo;s internal signal handler if there are any pending
signals.
This is primarily intended for those applications that use
a custom <code class="code">rl_getc_function</code> (see <a class="pxref" href="#Readline-Variables">Readline Variables</a>) and wish
to handle signals received while waiting for input.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcheck_005fsignals"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_check_signals</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fcheck_005fsignals"> &para;</a></span></dt>
<dd><p>If there are any pending signals, call Readline&rsquo;s internal signal
handling functions to process them.
<code class="code">rl_pending_signal()</code> can be used independently
to determine whether or not there are any pending signals.
</p></dd></dl>

<p>If an application does not wish Readline to catch <code class="code">SIGWINCH</code>,
it may call <code class="code">rl_resize_terminal()</code> or <code class="code">rl_set_screen_size()</code> 
to force Readline to update its idea of the terminal size when it receives
a <code class="code">SIGWINCH</code>.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fecho_005fsignal_005fchar"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_echo_signal_char</strong> <code class="def-code-arguments">(int sig)</code><a class="copiable-link" href="#index-rl_005fecho_005fsignal_005fchar"> &para;</a></span></dt>
<dd><p>If an application wishes to install its own signal handlers, but still
have Readline display characters that generate signals, calling this
function with <var class="var">sig</var> set to <code class="code">SIGINT</code>, <code class="code">SIGQUIT</code>, or
<code class="code">SIGTSTP</code> will display the character generating that signal.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fresize_005fterminal"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_resize_terminal</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fresize_005fterminal"> &para;</a></span></dt>
<dd><p>Update Readline&rsquo;s internal screen size by reading values from the kernel.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fset_005fscreen_005fsize"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_set_screen_size</strong> <code class="def-code-arguments">(int rows, int cols)</code><a class="copiable-link" href="#index-rl_005fset_005fscreen_005fsize"> &para;</a></span></dt>
<dd><p>Set Readline&rsquo;s idea of the terminal size to <var class="var">rows</var> rows and
<var class="var">cols</var> columns.
If either <var class="var">rows</var> or <var class="var">columns</var> is less than or equal to 0,
Readline doesn&rsquo;t change that terminal dimension.
This is intended to tell Readline the physical dimensions of the terminal,
and is used internally to calculate the maximum number of characters that
may appear on a single line and on the screen.
</p></dd></dl>

<p>If an application does not want to install a <code class="code">SIGWINCH</code> handler, but
is still interested in the screen dimensions, it may query Readline&rsquo;s idea
of the screen size.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fget_005fscreen_005fsize"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_get_screen_size</strong> <code class="def-code-arguments">(int *rows, int *cols)</code><a class="copiable-link" href="#index-rl_005fget_005fscreen_005fsize"> &para;</a></span></dt>
<dd><p>Return Readline&rsquo;s idea of the terminal&rsquo;s size in the
variables pointed to by the arguments.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005freset_005fscreen_005fsize"><span class="category-def">Function: </span><span><code class="def-type">void</code> <strong class="def-name">rl_reset_screen_size</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005freset_005fscreen_005fsize"> &para;</a></span></dt>
<dd><p>Cause Readline to reobtain the screen size and recalculate its dimensions.
</p></dd></dl>

<p>The following functions install and remove Readline&rsquo;s signal handlers.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fset_005fsignals"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_set_signals</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fset_005fsignals"> &para;</a></span></dt>
<dd><p>Install Readline&rsquo;s signal handler for <code class="code">SIGINT</code>, <code class="code">SIGQUIT</code>,
<code class="code">SIGTERM</code>, <code class="code">SIGHUP</code>, <code class="code">SIGALRM</code>, <code class="code">SIGTSTP</code>, <code class="code">SIGTTIN</code>,
<code class="code">SIGTTOU</code>, and <code class="code">SIGWINCH</code>, depending on the values of
<code class="code">rl_catch_signals</code> and <code class="code">rl_catch_sigwinch</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fclear_005fsignals"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_clear_signals</strong> <code class="def-code-arguments">(void)</code><a class="copiable-link" href="#index-rl_005fclear_005fsignals"> &para;</a></span></dt>
<dd><p>Remove all of the Readline signal handlers installed by
<code class="code">rl_set_signals()</code>.
</p></dd></dl>

<hr>
</div>
<div class="section-level-extent" id="Custom-Completers">
<div class="nav-panel">
<p>
Previous: <a href="#Readline-Signal-Handling" accesskey="p" rel="prev">Readline Signal Handling</a>, Up: <a href="#Programming-with-GNU-Readline" accesskey="u" rel="up">Programming with GNU Readline</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h3 class="section" id="Custom-Completers-1"><span>2.6 Custom Completers<a class="copiable-link" href="#Custom-Completers-1"> &para;</a></span></h3>
<a class="index-entry-id" id="index-application_002dspecific-completion-functions"></a>

<p>Typically, a program that reads commands from the user has a way of
disambiguating commands and data.
If your program is one of these, then
it can provide completion for commands, data, or both.
The following sections describe how your program and Readline
cooperate to provide this service.
</p>

<ul class="mini-toc">
<li><a href="#How-Completing-Works" accesskey="1">How Completing Works</a></li>
<li><a href="#Completion-Functions" accesskey="2">Completion Functions</a></li>
<li><a href="#Completion-Variables" accesskey="3">Completion Variables</a></li>
<li><a href="#A-Short-Completion-Example" accesskey="4">A Short Completion Example</a></li>
</ul>
<hr>
<div class="subsection-level-extent" id="How-Completing-Works">
<div class="nav-panel">
<p>
Next: <a href="#Completion-Functions" accesskey="n" rel="next">Completion Functions</a>, Up: <a href="#Custom-Completers" accesskey="u" rel="up">Custom Completers</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="How-Completing-Works-1"><span>2.6.1 How Completing Works<a class="copiable-link" href="#How-Completing-Works-1"> &para;</a></span></h4>

<p>In order to complete some text, the full list of possible completions
must be available.
That is, it is not possible to accurately
expand a partial word without knowing all of the possible words
which make sense in that context.
The Readline library provides
the user interface to completion, and two of the most common
completion functions:  filename and username.
For completing other types
of text, you must write your own completion function.
This section
describes exactly what such functions must do, and provides an example.
</p>
<p>There are three major functions used to perform completion:
</p>
<ol class="enumerate">
<li> The user-interface function <code class="code">rl_complete()</code>.
This function is called with the same arguments as other bindable
Readline functions: <var class="var">count</var> and <var class="var">invoking_key</var>.
It isolates the word to be completed and calls
<code class="code">rl_completion_matches()</code> to generate a list of possible completions.
It then either lists the possible completions, inserts the possible
completions, or actually performs the
completion, depending on which behavior is desired.

</li><li> The internal function <code class="code">rl_completion_matches()</code> uses an
application-supplied <em class="dfn">generator</em> function to generate the list of
possible matches, and then returns the array of these matches.
The caller should place the address of its generator function in
<code class="code">rl_completion_entry_function</code>.

</li><li> The generator function is called repeatedly from
<code class="code">rl_completion_matches()</code>, returning a string each time.
The arguments to the generator function are <var class="var">text</var> and <var class="var">state</var>.
<var class="var">text</var> is the partial word to be completed.
<var class="var">state</var> is zero the first time the function is called,
allowing the generator to perform any necessary initialization,
and a positive integer for each subsequent call.
The generator function returns
<code class="code">(char *)NULL</code> to inform <code class="code">rl_completion_matches()</code> that there are
no more possibilities left.
Usually the generator function computes the
list of possible completions when <var class="var">state</var> is zero, and returns them
one at a time on subsequent calls.
Each string the generator function
returns as a match must be allocated with <code class="code">malloc()</code>; Readline
frees the strings when it has finished with them.
Such a generator function is referred to as an
<em class="dfn">application-specific completion function</em>.

</li></ol>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcomplete"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_complete</strong> <code class="def-code-arguments">(int ignore, int invoking_key)</code><a class="copiable-link" href="#index-rl_005fcomplete"> &para;</a></span></dt>
<dd><p>Complete the word at or before point.
You have supplied the function that does the initial simple matching
selection algorithm (see <code class="code">rl_completion_matches()</code>).
The default is to do filename completion.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005fentry_005ffunction"><span class="category-def">Variable: </span><span><code class="def-type">rl_compentry_func_t *</code> <strong class="def-name">rl_completion_entry_function</strong><a class="copiable-link" href="#index-rl_005fcompletion_005fentry_005ffunction"> &para;</a></span></dt>
<dd><p>This is a pointer to the generator function for
<code class="code">rl_completion_matches()</code>.
If the value of <code class="code">rl_completion_entry_function</code> is
<code class="code">NULL</code> then Readline uses the default filename generator
function, <code class="code">rl_filename_completion_function()</code>.
An <em class="dfn">application-specific completion function</em> is a function whose
address is assigned to <code class="code">rl_completion_entry_function</code> and whose
return values are used to generate possible completions.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Completion-Functions">
<div class="nav-panel">
<p>
Next: <a href="#Completion-Variables" accesskey="n" rel="next">Completion Variables</a>, Previous: <a href="#How-Completing-Works" accesskey="p" rel="prev">How Completing Works</a>, Up: <a href="#Custom-Completers" accesskey="u" rel="up">Custom Completers</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Completion-Functions-1"><span>2.6.2 Completion Functions<a class="copiable-link" href="#Completion-Functions-1"> &para;</a></span></h4>

<p>Here is the complete list of callable completion functions present in
Readline.
</p>
<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcomplete_005finternal"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_complete_internal</strong> <code class="def-code-arguments">(int what_to_do)</code><a class="copiable-link" href="#index-rl_005fcomplete_005finternal"> &para;</a></span></dt>
<dd><p>Complete the word at or before point.
<var class="var">what_to_do</var> says what to do with the completion.
A value of &lsquo;<samp class="samp">?</samp>&rsquo; means list the possible completions.
&lsquo;<samp class="samp">TAB</samp>&rsquo; means do standard completion.
&lsquo;<samp class="samp">*</samp>&rsquo; means insert all of the possible completions.
&lsquo;<samp class="samp">!</samp>&rsquo; means to display all of the possible completions,
if there is more than one, as well as performing partial completion.
&lsquo;<samp class="samp">@</samp>&rsquo; is similar to &lsquo;<samp class="samp">!</samp>&rsquo;, but does not list possible completions
if the possible completions share a common prefix.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcomplete-1"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_complete</strong> <code class="def-code-arguments">(int ignore, int invoking_key)</code><a class="copiable-link" href="#index-rl_005fcomplete-1"> &para;</a></span></dt>
<dd><p>Complete the word at or before point.
You have supplied the function that does the initial simple
matching selection algorithm (see <code class="code">rl_completion_matches()</code> and
<code class="code">rl_completion_entry_function</code>).
The default is to do filename completion.
This calls <code class="code">rl_complete_internal()</code> with an
argument depending on <var class="var">invoking_key</var>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fpossible_005fcompletions"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_possible_completions</strong> <code class="def-code-arguments">(int count, int invoking_key)</code><a class="copiable-link" href="#index-rl_005fpossible_005fcompletions"> &para;</a></span></dt>
<dd><p>List the possible completions.
See description of <code class="code">rl_complete()</code>.
This calls <code class="code">rl_complete_internal()</code> with an argument of &lsquo;<samp class="samp">?</samp>&rsquo;.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005finsert_005fcompletions"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_insert_completions</strong> <code class="def-code-arguments">(int count, int invoking_key)</code><a class="copiable-link" href="#index-rl_005finsert_005fcompletions"> &para;</a></span></dt>
<dd><p>Insert the list of possible completions into the line, deleting the
partially-completed word.
See description of <code class="code">rl_complete()</code>.
This calls <code class="code">rl_complete_internal()</code> with an argument of &lsquo;<samp class="samp">*</samp>&rsquo;.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcompletion_005fmode"><span class="category-def">Function: </span><span><code class="def-type">int</code> <strong class="def-name">rl_completion_mode</strong> <code class="def-code-arguments">(rl_command_func_t *cfunc)</code><a class="copiable-link" href="#index-rl_005fcompletion_005fmode"> &para;</a></span></dt>
<dd><p>Returns the appropriate value to pass to <code class="code">rl_complete_internal()</code>
depending on whether <var class="var">cfunc</var> was called twice in succession and
the values of the <code class="code">show-all-if-ambiguous</code> and
<code class="code">show-all-if-unmodified</code> variables.
Application-specific completion functions may use this function to present
the same interface as <code class="code">rl_complete()</code>.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fcompletion_005fmatches"><span class="category-def">Function: </span><span><code class="def-type">char **</code> <strong class="def-name">rl_completion_matches</strong> <code class="def-code-arguments">(const char *text, rl_compentry_func_t *entry_func)</code><a class="copiable-link" href="#index-rl_005fcompletion_005fmatches"> &para;</a></span></dt>
<dd><p>Returns an array of strings which is a list of completions for <var class="var">text</var>.
If there are no completions, returns <code class="code">NULL</code>.
The first entry in the returned array is the substitution for <var class="var">text</var>.
The remaining entries are the possible completions.
The array is terminated with a <code class="code">NULL</code> pointer.
</p>
<p><var class="var">entry_func</var> is a function of two args, and returns a <code class="code">char *</code>.
The first argument is <var class="var">text</var>.
The second is a state argument;
it is zero on the first call, and non-zero on subsequent calls.
<var class="var">entry_func</var> returns a <code class="code">NULL</code> pointer to the caller
when there are no more matches.
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005ffilename_005fcompletion_005ffunction"><span class="category-def">Function: </span><span><code class="def-type">char *</code> <strong class="def-name">rl_filename_completion_function</strong> <code class="def-code-arguments">(const char *text, int state)</code><a class="copiable-link" href="#index-rl_005ffilename_005fcompletion_005ffunction"> &para;</a></span></dt>
<dd><p>A generator function for filename completion in the general case.
<var class="var">text</var> is a partial filename.
The Bash source is a useful reference for writing application-specific
completion functions (the Bash completion functions call this and other
Readline functions).
</p></dd></dl>

<dl class="first-deftypefn first-deftypefun-alias-first-deftypefn">
<dt class="deftypefn deftypefun-alias-deftypefn" id="index-rl_005fusername_005fcompletion_005ffunction"><span class="category-def">Function: </span><span><code class="def-type">char *</code> <strong class="def-name">rl_username_completion_function</strong> <code class="def-code-arguments">(const char *text, int state)</code><a class="copiable-link" href="#index-rl_005fusername_005fcompletion_005ffunction"> &para;</a></span></dt>
<dd><p>A completion generator for usernames.
<var class="var">text</var> contains a partial username preceded by a
random character (usually &lsquo;<samp class="samp">~</samp>&rsquo;).
As with all completion generators,
<var class="var">state</var> is zero on the first call and non-zero for subsequent calls.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="Completion-Variables">
<div class="nav-panel">
<p>
Next: <a href="#A-Short-Completion-Example" accesskey="n" rel="next">A Short Completion Example</a>, Previous: <a href="#Completion-Functions" accesskey="p" rel="prev">Completion Functions</a>, Up: <a href="#Custom-Completers" accesskey="u" rel="up">Custom Completers</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="Completion-Variables-1"><span>2.6.3 Completion Variables<a class="copiable-link" href="#Completion-Variables-1"> &para;</a></span></h4>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005fentry_005ffunction-1"><span class="category-def">Variable: </span><span><code class="def-type">rl_compentry_func_t *</code> <strong class="def-name">rl_completion_entry_function</strong><a class="copiable-link" href="#index-rl_005fcompletion_005fentry_005ffunction-1"> &para;</a></span></dt>
<dd><p>A pointer to the generator function for <code class="code">rl_completion_matches()</code>.
<code class="code">NULL</code> means to use <code class="code">rl_filename_completion_function()</code>,
the default filename completer.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fattempted_005fcompletion_005ffunction"><span class="category-def">Variable: </span><span><code class="def-type">rl_completion_func_t *</code> <strong class="def-name">rl_attempted_completion_function</strong><a class="copiable-link" href="#index-rl_005fattempted_005fcompletion_005ffunction"> &para;</a></span></dt>
<dd><p>A pointer to an alternative function to create matches.
The function is called with <var class="var">text</var>, <var class="var">start</var>, and <var class="var">end</var>.
<var class="var">start</var> and <var class="var">end</var> are indices in <code class="code">rl_line_buffer</code> defining
the boundaries of <var class="var">text</var>, which is a character string.
If this function exists and returns <code class="code">NULL</code>, or if this variable is
set to <code class="code">NULL</code>, then <code class="code">rl_complete()</code> will call the value of
<code class="code">rl_completion_entry_function</code> to generate matches, otherwise
completion will use the array of strings this function returns.
If this function sets the <code class="code">rl_attempted_completion_over</code>
variable to a non-zero value, Readline will not perform its default
completion even if this function returns no matches.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005ffilename_005fquoting_005ffunction"><span class="category-def">Variable: </span><span><code class="def-type">rl_quote_func_t *</code> <strong class="def-name">rl_filename_quoting_function</strong><a class="copiable-link" href="#index-rl_005ffilename_005fquoting_005ffunction"> &para;</a></span></dt>
<dd><p>A pointer to a function that will quote a filename in an
application-specific fashion.
Readline calls this function during filename completion
if one of the characters in <code class="code">rl_filename_quote_characters</code>
appears in a completed filename.
The function is called with
<var class="var">text</var>, <var class="var">match_type</var>, and <var class="var">quote_pointer</var>.
The <var class="var">text</var> is the filename to be quoted.
The <var class="var">match_type</var> is either <code class="code">SINGLE_MATCH</code>,
if there is only one completion match, or <code class="code">MULT_MATCH</code>.
Some functions use this to decide whether or not to
insert a closing quote character.
The <var class="var">quote_pointer</var> is a pointer
to any opening quote character the user typed.
Some functions choose to reset this character if they decide to quote
the filename in a different style.
It&rsquo;s preferable to preserve the user&rsquo;s quoting as much as possible &ndash;
it&rsquo;s less disruptive.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005ffilename_005fdequoting_005ffunction"><span class="category-def">Variable: </span><span><code class="def-type">rl_dequote_func_t *</code> <strong class="def-name">rl_filename_dequoting_function</strong><a class="copiable-link" href="#index-rl_005ffilename_005fdequoting_005ffunction"> &para;</a></span></dt>
<dd><p>A pointer to a function that will remove application-specific quoting
characters from a filename before attempting completion,
so those characters do not interfere with matching the text against
names in the filesystem.
It is called with <var class="var">text</var>, the text of the word
to be dequoted, and <var class="var">quote_char</var>, which is the quoting character 
that delimits the filename (usually &lsquo;<samp class="samp">'</samp>&rsquo; or &lsquo;<samp class="samp">&quot;</samp>&rsquo;).
If <var class="var">quote_char</var> is zero, the filename was not in a quoted string.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fchar_005fis_005fquoted_005fp"><span class="category-def">Variable: </span><span><code class="def-type">rl_linebuf_func_t *</code> <strong class="def-name">rl_char_is_quoted_p</strong><a class="copiable-link" href="#index-rl_005fchar_005fis_005fquoted_005fp"> &para;</a></span></dt>
<dd><p>A pointer to a function to call that determines whether or not a specific
character in the line buffer is quoted, according to whatever quoting
mechanism the application uses.
The function is called with two arguments:
<var class="var">text</var>, the text of the line,
and <var class="var">index</var>, the index of the character in the line.
It is used to decide whether a character found in
<code class="code">rl_completer_word_break_characters</code> should be
used to break words for the completer.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fignore_005fsome_005fcompletions_005ffunction"><span class="category-def">Variable: </span><span><code class="def-type">rl_compignore_func_t *</code> <strong class="def-name">rl_ignore_some_completions_function</strong><a class="copiable-link" href="#index-rl_005fignore_005fsome_005fcompletions_005ffunction"> &para;</a></span></dt>
<dd><p>Readline calls this function, if defined, when filename
completion is done, after all the matching names have been generated.
It is passed a <code class="code">NULL</code> terminated array of matches.
The first element (<code class="code">matches[0]</code>) is the maximal substring
common to all matches.
This function can re-arrange the list of matches as required, but
must free each element it deletes from the array.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fdirectory_005fcompletion_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_icppfunc_t *</code> <strong class="def-name">rl_directory_completion_hook</strong><a class="copiable-link" href="#index-rl_005fdirectory_005fcompletion_005fhook"> &para;</a></span></dt>
<dd><p>This function, if defined, is allowed to modify the directory portion
of filenames during completion.
It could be used to expand symbolic links or shell variables in pathnames.
It is called with the address of a string (the current directory name) as an
argument, and may modify that string.
If the function replaces the string with a new string, it
should free the old value.
Any modified directory name should have a trailing slash.
The modified value will be used as part of the completion, replacing
the directory portion of the pathname the user typed.
At the least, even if no other expansion is performed, this function should
remove any quote characters from the directory name, because its result will
be passed directly to <code class="code">opendir()</code>.
</p>
<p>The directory completion hook returns an integer that should be non-zero if
the function modifies its directory argument.
The function should not modify the directory argument if it returns 0.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fdirectory_005frewrite_005fhook_003b"><span class="category-def">Variable: </span><span><code class="def-type">rl_icppfunc_t *</code> <strong class="def-name">rl_directory_rewrite_hook;</strong><a class="copiable-link" href="#index-rl_005fdirectory_005frewrite_005fhook_003b"> &para;</a></span></dt>
<dd><p>If non-zero, this is the address of a function to call when completing
a directory name.
This function takes the address of the directory name
to be modified as an argument.
Unlike <code class="code">rl_directory_completion_hook</code>,
it only modifies the directory name used in <code class="code">opendir()</code>,
not what Readline displays when it prints or inserts
the possible completions.
Readline calls this before rl_directory_completion_hook.
At the least, even if no other expansion is performed, this function should
remove any quote characters from the directory name, because its result will
be passed directly to <code class="code">opendir()</code>.
</p>
<p>The directory rewrite hook returns an integer that should be non-zero if
the function modifies its directory argument.
The function should not modify the directory argument if it returns 0.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005ffilename_005fstat_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_icppfunc_t *</code> <strong class="def-name">rl_filename_stat_hook</strong><a class="copiable-link" href="#index-rl_005ffilename_005fstat_005fhook"> &para;</a></span></dt>
<dd><p>If non-zero, this is the address of a function for the completer to
call before deciding which character to append to a completed name.
This function modifies its filename name argument, and Readline passes
the modified value to <code class="code">stat()</code>
to determine the file&rsquo;s type and characteristics.
This function does not need to remove quote characters from the filename.
</p>
<p>The stat hook returns an integer that should be non-zero if
the function modifies its directory argument.
The function should not modify the directory argument if it returns 0.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005ffilename_005frewrite_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_dequote_func_t *</code> <strong class="def-name">rl_filename_rewrite_hook</strong><a class="copiable-link" href="#index-rl_005ffilename_005frewrite_005fhook"> &para;</a></span></dt>
<dd><p>If non-zero, this is the address of a function
for Readline to call when reading
directory entries from the filesystem for completion and comparing
them to the filename portion of the partial word being completed.
It modifies the filesystem entries,
as opposed to <code class="code">rl_completion_rewrite_hook</code>,
which modifies the word being completed.
The function takes two arguments:
<var class="var">fname</var>, the filename to be converted,
and <var class="var">fnlen</var>, its length in bytes.
It must either return its first argument (if no conversion takes place)
or the converted filename in newly-allocated memory.
The function should perform any necessary application or system-specific
conversion on the filename, such as converting between character sets
or converting from a filesystem format to a character input format.
Readline compares the converted form against the word to be completed,
and, if it matches, adds it to the list of matches.
Readline will free the allocated string.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005frewrite_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_dequote_func_t *</code> <strong class="def-name">rl_completion_rewrite_hook</strong><a class="copiable-link" href="#index-rl_005fcompletion_005frewrite_005fhook"> &para;</a></span></dt>
<dd><p>If non-zero, this is the address of a function
for Readline to call before
comparing the filename portion of a word to be completed with directory
entries from the filesystem.
It modifies the word being completed,
as opposed to <code class="code">rl_filename_rewrite_hook</code>,
which modifies filesystem entries.
The function takes two arguments:
<var class="var">fname</var>, the word to be converted,
after any <code class="code">rl_filename_dequoting_function</code> has been applied,
and <var class="var">fnlen</var>, its length in bytes.
It must either return its first argument (if no conversion takes place)
or the converted filename in newly-allocated memory.
The function should perform any necessary application or system-specific
conversion on the filename, such as converting between character sets or
converting from a character input format to some other format.
Readline compares the converted form against directory entries, after
their potential modification by <code class="code">rl_filename_rewrite_hook</code>,
and adds any matches to the list of matches.
Readline will free the allocated string.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005fdisplay_005fmatches_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_compdisp_func_t *</code> <strong class="def-name">rl_completion_display_matches_hook</strong><a class="copiable-link" href="#index-rl_005fcompletion_005fdisplay_005fmatches_005fhook"> &para;</a></span></dt>
<dd><p>If non-zero, then this is the address of a function to call when
completing a word would normally display the list of possible matches.
Readline calls this function instead of displaying the list itself.
It takes three arguments:
(<code class="code">char **</code><var class="var">matches</var>, <code class="code">int</code> <var class="var">num_matches</var>, <code class="code">int</code> <var class="var">max_length</var>)
where <var class="var">matches</var> is the array of matching strings,
<var class="var">num_matches</var> is the number of strings in that array, and
<var class="var">max_length</var> is the length of the longest string in that array.
Readline provides a convenience function, <code class="code">rl_display_match_list</code>,
that takes care of doing the display to Readline&rsquo;s output stream.
You may call that function from this hook.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fbasic_005fword_005fbreak_005fcharacters"><span class="category-def">Variable: </span><span><code class="def-type">const char *</code> <strong class="def-name">rl_basic_word_break_characters</strong><a class="copiable-link" href="#index-rl_005fbasic_005fword_005fbreak_005fcharacters"> &para;</a></span></dt>
<dd><p>The basic list of characters that signal a break between words for the
completer routine.
The default value of this variable is the characters
which break words for completion in Bash:
<code class="code">&quot; \t\n\&quot;\\'`@$&gt;&lt;=;|&amp;{(&quot;</code>.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fbasic_005fquote_005fcharacters"><span class="category-def">Variable: </span><span><code class="def-type">const char *</code> <strong class="def-name">rl_basic_quote_characters</strong><a class="copiable-link" href="#index-rl_005fbasic_005fquote_005fcharacters"> &para;</a></span></dt>
<dd><p>A list of quote characters which can cause a word break.
The default value includes single and double quotes.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompleter_005fword_005fbreak_005fcharacters"><span class="category-def">Variable: </span><span><code class="def-type">const char *</code> <strong class="def-name">rl_completer_word_break_characters</strong><a class="copiable-link" href="#index-rl_005fcompleter_005fword_005fbreak_005fcharacters"> &para;</a></span></dt>
<dd><p>The list of characters that signal a break between words for
<code class="code">rl_complete_internal()</code>.
These characters determine how Readline decides what to complete.
The default list is the value of
<code class="code">rl_basic_word_break_characters</code>.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005fword_005fbreak_005fhook"><span class="category-def">Variable: </span><span><code class="def-type">rl_cpvfunc_t *</code> <strong class="def-name">rl_completion_word_break_hook</strong><a class="copiable-link" href="#index-rl_005fcompletion_005fword_005fbreak_005fhook"> &para;</a></span></dt>
<dd><p>If non-zero, this is the address of a function to call when Readline is
deciding where to separate words for word completion.
It should return a character string like
<code class="code">rl_completer_word_break_characters</code> to be
used to perform the current completion.
The function may choose to set
<code class="code">rl_completer_word_break_characters</code> itself.
If the function returns <code class="code">NULL</code>, Readline uses
<code class="code">rl_completer_word_break_characters</code>.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompleter_005fquote_005fcharacters"><span class="category-def">Variable: </span><span><code class="def-type">const char *</code> <strong class="def-name">rl_completer_quote_characters</strong><a class="copiable-link" href="#index-rl_005fcompleter_005fquote_005fcharacters"> &para;</a></span></dt>
<dd><p>A list of characters which can be used to quote a substring of the line.
Completion occurs on the entire substring, and within the substring,
<code class="code">rl_completer_word_break_characters</code> are treated as any other character,
unless they also appear within this list.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005ffilename_005fquote_005fcharacters"><span class="category-def">Variable: </span><span><code class="def-type">const char *</code> <strong class="def-name">rl_filename_quote_characters</strong><a class="copiable-link" href="#index-rl_005ffilename_005fquote_005fcharacters"> &para;</a></span></dt>
<dd><p>A list of characters that cause Readline to quote a filename
when they appear in a completed filename.
The default is the null string.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fspecial_005fprefixes"><span class="category-def">Variable: </span><span><code class="def-type">const char *</code> <strong class="def-name">rl_special_prefixes</strong><a class="copiable-link" href="#index-rl_005fspecial_005fprefixes"> &para;</a></span></dt>
<dd><p>The list of characters that are word break characters, but should be
left in <var class="var">text</var> when it is passed to the completion function.
Programs can use this to help determine what kind of completing to do.
For instance, Bash sets this variable to &quot;$@&quot; so that it can complete
shell variables and hostnames.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005fquery_005fitems"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_completion_query_items</strong><a class="copiable-link" href="#index-rl_005fcompletion_005fquery_005fitems"> &para;</a></span></dt>
<dd><p>This determines the maximum number of items
that possible-completions will display unconditionally.
If there are more possible completions than this,
Readline asks the user for confirmation before displaying them.
The default value is 100.
A negative value 
indicates that Readline should never ask for confirmation.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005fappend_005fcharacter"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_completion_append_character</strong><a class="copiable-link" href="#index-rl_005fcompletion_005fappend_005fcharacter"> &para;</a></span></dt>
<dd><p>When a single completion alternative matches at the end of the command
line, Readline appends this character to the inserted completion text.
The default is a space character (&lsquo;<samp class="samp"> </samp>&rsquo;).
Setting this to the null
character (&lsquo;<samp class="samp">\0</samp>&rsquo;) prevents anything being appended automatically.
This can be changed in application-specific completion functions to
provide the &ldquo;most sensible word separator character&rdquo; according to
an application-specific command line syntax specification.
It is set to the default before calling any application-specific completion
function, and may only be changed within such a function.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005fsuppress_005fappend"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_completion_suppress_append</strong><a class="copiable-link" href="#index-rl_005fcompletion_005fsuppress_005fappend"> &para;</a></span></dt>
<dd><p>If non-zero, Readline will not append the
<var class="var">rl_completion_append_character</var> to
matches at the end of the command line, as described above.
It is set to 0 before calling any application-specific completion function,
and may only be changed within such a function.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005fsuppress_005fquote"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_completion_suppress_quote</strong><a class="copiable-link" href="#index-rl_005fcompletion_005fsuppress_005fquote"> &para;</a></span></dt>
<dd><p>If non-zero, Readline does not append a matching quote character when
performing completion on a quoted string.
It is set to 0 before calling any application-specific completion function,
and may only be changed within such a function.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005ffound_005fquote"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_completion_found_quote</strong><a class="copiable-link" href="#index-rl_005fcompletion_005ffound_005fquote"> &para;</a></span></dt>
<dd><p>When Readline is completing quoted text, it sets this variable
to a non-zero value if the word being completed contains or is delimited
by any quoting characters, including backslashes.
This is set before calling any application-specific completion function.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005fquote_005fcharacter"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_completion_quote_character</strong><a class="copiable-link" href="#index-rl_005fcompletion_005fquote_005fcharacter"> &para;</a></span></dt>
<dd><p>When Readline is completing quoted text, as delimited by one of the
characters in <var class="var">rl_completer_quote_characters</var>, it sets this variable
to the quoting character it found.
This is set before calling any application-specific completion function.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005fmark_005fsymlink_005fdirs"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_completion_mark_symlink_dirs</strong><a class="copiable-link" href="#index-rl_005fcompletion_005fmark_005fsymlink_005fdirs"> &para;</a></span></dt>
<dd><p>If non-zero, Readline appends a slash to completed filenames that are
symbolic links to directory names, subject to the value of the
user-settable <var class="var">mark-directories</var> variable.
This variable exists so that application-specific completion functions
can override the user&rsquo;s global preference (set via the
<var class="var">mark-symlinked-directories</var> Readline variable) if appropriate.
This variable is set to the user&rsquo;s preference before calling any
application-specific completion function,
so unless that function modifies the value,
Readline will honor the user&rsquo;s preferences.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fignore_005fcompletion_005fduplicates"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_ignore_completion_duplicates</strong><a class="copiable-link" href="#index-rl_005fignore_005fcompletion_005fduplicates"> &para;</a></span></dt>
<dd><p>If non-zero, then Readline removes duplicates in the set of possible
completions.
The default is 1.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005ffilename_005fcompletion_005fdesired"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_filename_completion_desired</strong><a class="copiable-link" href="#index-rl_005ffilename_005fcompletion_005fdesired"> &para;</a></span></dt>
<dd><p>A non-zero value means that Readline should treat the results of the
matches as filenames.
This is <em class="emph">always</em> zero when completion is attempted,
and can only be changed
within an application-specific completion function.
If it is set to a
non-zero value by such a function, Readline
appends a slash to directory names
and attempts to quote completed filenames if they contain any
characters in <code class="code">rl_filename_quote_characters</code> and
<code class="code">rl_filename_quoting_desired</code> is set to a non-zero value.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005ffilename_005fquoting_005fdesired"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_filename_quoting_desired</strong><a class="copiable-link" href="#index-rl_005ffilename_005fquoting_005fdesired"> &para;</a></span></dt>
<dd><p>A non-zero value means that Readline should quote the results of the 
matches using double quotes (or an application-specific quoting mechanism)
if the completed filename contains any characters in
<code class="code">rl_filename_quote_chars</code>.
This is <em class="emph">always</em> non-zero when completion is attempted,
and can only be changed within an
application-specific completion function.
The quoting is performed via a call to the function pointed to
by <code class="code">rl_filename_quoting_function</code>.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005ffull_005fquoting_005fdesired"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_full_quoting_desired</strong><a class="copiable-link" href="#index-rl_005ffull_005fquoting_005fdesired"> &para;</a></span></dt>
<dd><p>A non-zero value means that Readline should apply filename-style quoting,
including any application-specified quoting mechanism,
to all completion matches even if it is not otherwise treating the
matches as filenames.
This is <em class="emph">always</em> zero when completion is attempted,
and can only be changed within an
application-specific completion function.
The quoting is performed via a call to the function pointed to
by <code class="code">rl_filename_quoting_function</code>.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fattempted_005fcompletion_005fover"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_attempted_completion_over</strong><a class="copiable-link" href="#index-rl_005fattempted_005fcompletion_005fover"> &para;</a></span></dt>
<dd><p>If an application-specific completion function assigned to
<code class="code">rl_attempted_completion_function</code> sets this variable to a non-zero
value, Readline will not perform its default filename completion even
if the application&rsquo;s completion function returns no matches.
It should be set only by an application&rsquo;s completion function.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fsort_005fcompletion_005fmatches"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_sort_completion_matches</strong><a class="copiable-link" href="#index-rl_005fsort_005fcompletion_005fmatches"> &para;</a></span></dt>
<dd><p>If an application sets this variable to 0, Readline will not sort the
list of completions (which implies that it cannot remove any duplicate
completions).
The default value is 1, which means that Readline will
sort the completions and, depending on the value of
<code class="code">rl_ignore_completion_duplicates</code>, will attempt to remove
duplicate matches.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005ftype"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_completion_type</strong><a class="copiable-link" href="#index-rl_005fcompletion_005ftype"> &para;</a></span></dt>
<dd><p>Set to a character describing the type of completion Readline is currently
attempting; see the description of <code class="code">rl_complete_internal()</code>
(see <a class="pxref" href="#Completion-Functions">Completion Functions</a>) for the list of characters.
This is set to the appropriate value before calling
any application-specific completion function,
so these functions can present
the same interface as <code class="code">rl_complete()</code>.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005fcompletion_005finvoking_005fkey"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_completion_invoking_key</strong><a class="copiable-link" href="#index-rl_005fcompletion_005finvoking_005fkey"> &para;</a></span></dt>
<dd><p>Set to the final character in the key sequence that invoked one of the
completion functions that call <code class="code">rl_complete_internal()</code>.
This is set to the appropriate value before calling
any application-specific completion function.
</p></dd></dl>

<dl class="first-deftypevr first-deftypevar-alias-first-deftypevr">
<dt class="deftypevr deftypevar-alias-deftypevr" id="index-rl_005finhibit_005fcompletion"><span class="category-def">Variable: </span><span><code class="def-type">int</code> <strong class="def-name">rl_inhibit_completion</strong><a class="copiable-link" href="#index-rl_005finhibit_005fcompletion"> &para;</a></span></dt>
<dd><p>If this variable is non-zero, Readline does not perform completion,
even if a key binding indicates it should.
The completion character
is inserted as if it were bound to <code class="code">self-insert</code>.
</p></dd></dl>

<hr>
</div>
<div class="subsection-level-extent" id="A-Short-Completion-Example">
<div class="nav-panel">
<p>
Previous: <a href="#Completion-Variables" accesskey="p" rel="prev">Completion Variables</a>, Up: <a href="#Custom-Completers" accesskey="u" rel="up">Custom Completers</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h4 class="subsection" id="A-Short-Completion-Example-1"><span>2.6.4 A Short Completion Example<a class="copiable-link" href="#A-Short-Completion-Example-1"> &para;</a></span></h4>

<p>Here is a small application demonstrating the use of the GNU Readline
library.
It is called <code class="code">fileman</code>, and the source code resides in
<samp class="file">examples/fileman.c</samp>.
This sample application provides
command name completion, line editing features,
and access to the history list.
</p>
<div class="example smallexample">
<pre class="example-preformatted">/* fileman.c -- A tiny application which demonstrates how to use the
   GNU Readline library.  This application interactively allows users
   to manipulate files and their modes. */

#ifdef HAVE_CONFIG_H
#  include &lt;config.h&gt;
#endif

#include &lt;sys/types.h&gt;
#ifdef HAVE_SYS_FILE_H
#  include &lt;sys/file.h&gt;
#endif
#include &lt;sys/stat.h&gt;

#ifdef HAVE_UNISTD_H
#  include &lt;unistd.h&gt;
#endif

#include &lt;fcntl.h&gt;
#include &lt;stdio.h&gt;
#include &lt;errno.h&gt;
#include &lt;locale.h&gt;

#if defined (HAVE_STRING_H)
#  include &lt;string.h&gt;
#else /* !HAVE_STRING_H */
#  include &lt;strings.h&gt;
#endif /* !HAVE_STRING_H */

#ifdef HAVE_STDLIB_H
#  include &lt;stdlib.h&gt;
#endif

#include &lt;time.h&gt;

#include &lt;readline/readline.h&gt;
#include &lt;readline/history.h&gt;

extern char *xmalloc PARAMS((size_t));

/* The names of functions that actually do the manipulation. */
int com_list PARAMS((char *));
int com_view PARAMS((char *));
int com_rename PARAMS((char *));
int com_stat PARAMS((char *));
int com_pwd PARAMS((char *));
int com_delete PARAMS((char *));
int com_help PARAMS((char *));
int com_cd PARAMS((char *));
int com_quit PARAMS((char *));

/* A structure which contains information on the commands this program
   can understand. */

typedef struct {
  char *name;			/* User printable name of the function. */
  rl_icpfunc_t *func;		/* Function to call to do the job. */
  char *doc;			/* Documentation for this function.  */
} COMMAND;

COMMAND commands[] = {
  { &quot;cd&quot;, com_cd, &quot;Change to directory DIR&quot; },
  { &quot;delete&quot;, com_delete, &quot;Delete FILE&quot; },
  { &quot;help&quot;, com_help, &quot;Display this text&quot; },
  { &quot;?&quot;, com_help, &quot;Synonym for `help'&quot; },
  { &quot;list&quot;, com_list, &quot;List files in DIR&quot; },
  { &quot;ls&quot;, com_list, &quot;Synonym for `list'&quot; },
  { &quot;pwd&quot;, com_pwd, &quot;Print the current working directory&quot; },
  { &quot;quit&quot;, com_quit, &quot;Quit using Fileman&quot; },
  { &quot;rename&quot;, com_rename, &quot;Rename FILE to NEWNAME&quot; },
  { &quot;stat&quot;, com_stat, &quot;Print out statistics on FILE&quot; },
  { &quot;view&quot;, com_view, &quot;View the contents of FILE&quot; },
  { (char *)NULL, (rl_icpfunc_t *)NULL, (char *)NULL }
};

/* Forward declarations. */
char *stripwhite (char *);
COMMAND *find_command (char *);

/* The name of this program, as taken from argv[0]. */
char *progname;

/* When non-zero, this global means the user is done using this program. */
int done;

char *
dupstr (char *s)
{
  char *r;

  r = xmalloc (strlen (s) + 1);
  strcpy (r, s);
  return (r);
}

int
main (int argc, char **argv)
{
  char *line, *s;

  setlocale (LC_ALL, &quot;&quot;);

  progname = argv[0];

  initialize_readline ();	/* Bind our completer. */

  /* Loop reading and executing lines until the user quits. */
  for ( ; done == 0; )
    {
      line = readline (&quot;FileMan: &quot;);

      if (!line)
        break;

      /* Remove leading and trailing whitespace from the line.
         Then, if there is anything left, add it to the history list
         and execute it. */
      s = stripwhite (line);

      if (*s)
        {
          add_history (s);
          execute_line (s);
        }

      free (line);
    }
  exit (0);
}

/* Execute a command line. */
int
execute_line (char *line)
{
  register int i;
  COMMAND *command;
  char *word;

  /* Isolate the command word. */
  i = 0;
  while (line[i] &amp;&amp; whitespace (line[i]))
    i++;
  word = line + i;

  while (line[i] &amp;&amp; !whitespace (line[i]))
    i++;

  if (line[i])
    line[i++] = '\0';

  command = find_command (word);

  if (!command)
    {
      fprintf (stderr, &quot;%s: No such command for FileMan.\n&quot;, word);
      return (-1);
    }

  /* Get argument to command, if any. */
  while (whitespace (line[i]))
    i++;

  word = line + i;

  /* Call the function. */
  return ((*(command-&gt;func)) (word));
}

/* Look up NAME as the name of a command, and return a pointer to that
   command.  Return a NULL pointer if NAME isn't a command name. */
COMMAND *
find_command (char *name)
{
  register int i;

  for (i = 0; commands[i].name; i++)
    if (strcmp (name, commands[i].name) == 0)
      return (&amp;commands[i]);

  return ((COMMAND *)NULL);
}

/* Strip whitespace from the start and end of STRING.  Return a pointer
   into STRING. */
char *
stripwhite (char *string)
{
  register char *s, *t;

  for (s = string; whitespace (*s); s++)
    ;
    
  if (*s == 0)
    return (s);

  t = s + strlen (s) - 1;
  while (t &gt; s &amp;&amp; whitespace (*t))
    t--;
  *++t = '\0';

  return s;
}

/* **************************************************************** */
/*                                                                  */
/*                  Interface to Readline Completion                */
/*                                                                  */
/* **************************************************************** */

char *command_generator (const char *, int);
char **fileman_completion (const char *, int, int);

/* Tell the GNU Readline library how to complete.  We want to try to complete
   on command names if this is the first word in the line, or on filenames
   if not. */
void
initialize_readline (void)
{
  /* Allow conditional parsing of the ~/.inputrc file. */
  rl_readline_name = &quot;FileMan&quot;;

  /* Tell the completer that we want a crack first. */
  rl_attempted_completion_function = fileman_completion;
}

/* Attempt to complete on the contents of TEXT.  START and END bound the
   region of rl_line_buffer that contains the word to complete.  TEXT is
   the word to complete.  We can use the entire contents of rl_line_buffer
   in case we want to do some simple parsing.  Return the array of matches,
   or NULL if there aren't any. */
char **
fileman_completion (const char *text, int start, int end)
{
  char **matches;

  matches = (char **)NULL;

  /* If this word is at the start of the line, then it is a command
     to complete.  Otherwise it is the name of a file in the current
     directory. */
  if (start == 0)
    matches = rl_completion_matches (text, command_generator);

  return (matches);
}

/* Generator function for command completion.  STATE lets us know whether
   to start from scratch; without any state (i.e. STATE == 0), then we
   start at the top of the list. */
char *
command_generator (const char *text, int state)
{
  static int list_index, len;
  char *name;

  /* If this is a new word to complete, initialize now.  This includes
     saving the length of TEXT for efficiency, and initializing the index
     variable to 0. */
  if (!state)
    {
      list_index = 0;
      len = strlen (text);
    }

  /* Return the next name which partially matches from the command list. */
  while (name = commands[list_index].name)
    {
      list_index++;

      if (strncmp (name, text, len) == 0)
        return (dupstr(name));
    }

  /* If no names matched, then return NULL. */
  return ((char *)NULL);
}

/* **************************************************************** */
/*                                                                  */
/*                       FileMan Commands                           */
/*                                                                  */
/* **************************************************************** */

/* String to pass to system ().  This is for the LIST, VIEW and RENAME
   commands. */
static char syscom[1024];

/* List the file(s) named in arg. */
int
com_list (char *arg)
{
  if (!arg)
    arg = &quot;&quot;;

  snprintf (syscom, sizeof (syscom), &quot;ls -FClg %s&quot;, arg);
  return (system (syscom));
}

int
com_view (char *arg)
{
  if (!valid_argument (&quot;view&quot;, arg))
    return 1;

#if defined (__MSDOS__)
  /* more.com doesn't grok slashes in pathnames */
  snprintf (syscom, sizeof (syscom), &quot;less %s&quot;, arg);
#else
  snprintf (syscom, sizeof (syscom), &quot;more %s&quot;, arg);
#endif
  return (system (syscom));
}

int
com_rename (char *arg)
{
  too_dangerous (&quot;rename&quot;);
  return (1);
}

int
com_stat (char *arg)
{
  struct stat finfo;

  if (!valid_argument (&quot;stat&quot;, arg))
    return (1);

  if (stat (arg, &amp;finfo) == -1)
    {
      perror (arg);
      return (1);
    }

  printf (&quot;Statistics for `%s':\n&quot;, arg);

  printf (&quot;%s has %d link%s, and is %d byte%s in length.\n&quot;,
	  arg,
          finfo.st_nlink,
          (finfo.st_nlink == 1) ? &quot;&quot; : &quot;s&quot;,
          finfo.st_size,
          (finfo.st_size == 1) ? &quot;&quot; : &quot;s&quot;);
  printf (&quot;Inode Last Change at: %s&quot;, ctime (&amp;finfo.st_ctime));
  printf (&quot;      Last access at: %s&quot;, ctime (&amp;finfo.st_atime));
  printf (&quot;    Last modified at: %s&quot;, ctime (&amp;finfo.st_mtime));
  return (0);
}

int
com_delete (char *arg)
{
  too_dangerous (&quot;delete&quot;);
  return (1);
}

/* Print out help for ARG, or for all of the commands if ARG is
   not present. */
int
com_help (char *arg)
{
  register int i;
  int printed = 0;

  for (i = 0; commands[i].name; i++)
    {
      if (!*arg || (strcmp (arg, commands[i].name) == 0))
        {
          printf (&quot;%s\t\t%s.\n&quot;, commands[i].name, commands[i].doc);
          printed++;
        }
    }

  if (!printed)
    {
      printf (&quot;No commands match `%s'.  Possibilities are:\n&quot;, arg);

      for (i = 0; commands[i].name; i++)
        {
          /* Print in six columns. */
          if (printed == 6)
            {
              printed = 0;
              printf (&quot;\n&quot;);
            }

          printf (&quot;%s\t&quot;, commands[i].name);
          printed++;
        }

      if (printed)
        printf (&quot;\n&quot;);
    }
  return (0);
}

/* Change to the directory ARG. */
int
com_cd (char *arg)
{
  if (chdir (arg) == -1)
    {
      perror (arg);
      return 1;
    }

  com_pwd (&quot;&quot;);
  return (0);
}

/* Print out the current working directory. */
int
com_pwd (char *ignore)
{
  char dir[1024], *s;

  s = getcwd (dir, sizeof(dir) - 1);
  if (s == 0)
    {
      printf (&quot;Error getting pwd: %s\n&quot;, dir);
      return 1;
    }

  printf (&quot;Current directory is %s\n&quot;, dir);
  return 0;
}

/* The user wishes to quit using this program.  Just set DONE non-zero. */
int
com_quit (char *arg)
{
  done = 1;
  return (0);
}

/* Function which tells you that you can't do this. */
void
too_dangerous (char *caller)
{
  fprintf (stderr,
           &quot;%s: Too dangerous for me to distribute.  Write it yourself.\n&quot;,
           caller);
}

/* Return non-zero if ARG is a valid argument for CALLER, else print
   an error message and return zero. */
int
valid_argument (char *caller, char *arg)
{
  if (!arg || !*arg)
    {
      fprintf (stderr, &quot;%s: Argument required.\n&quot;, caller);
      return (0);
    }

  return (1);
}
</pre></div>

<hr>
</div>
</div>
</div>
<div class="appendix-level-extent" id="GNU-Free-Documentation-License">
<div class="nav-panel">
<p>
Next: <a href="#Concept-Index" accesskey="n" rel="next">Concept Index</a>, Previous: <a href="#Programming-with-GNU-Readline" accesskey="p" rel="prev">Programming with GNU Readline</a>, Up: <a href="#Top" accesskey="u" rel="up">GNU Readline Library</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h2 class="appendix" id="GNU-Free-Documentation-License-1"><span>Appendix A GNU Free Documentation License<a class="copiable-link" href="#GNU-Free-Documentation-License-1"> &para;</a></span></h2>

<div class="center">Version 1.3, 3 November 2008
</div>

<div class="display">
<pre class="display-preformatted">Copyright &copy; 2000, 2001, 2002, 2007, 2008 Free Software Foundation, Inc.
<a class="uref" href="http://fsf.org/">http://fsf.org/</a>

Everyone is permitted to copy and distribute verbatim copies
of this license document, but changing it is not allowed.
</pre></div>

<ol class="enumerate" start="0">
<li> PREAMBLE

<p>The purpose of this License is to make a manual, textbook, or other
functional and useful document <em class="dfn">free</em> in the sense of freedom: to
assure everyone the effective freedom to copy and redistribute it,
with or without modifying it, either commercially or noncommercially.
Secondarily, this License preserves for the author and publisher a way
to get credit for their work, while not being considered responsible
for modifications made by others.
</p>
<p>This License is a kind of &ldquo;copyleft&rdquo;, which means that derivative
works of the document must themselves be free in the same sense.  It
complements the GNU General Public License, which is a copyleft
license designed for free software.
</p>
<p>We have designed this License in order to use it for manuals for free
software, because free software needs free documentation: a free
program should come with manuals providing the same freedoms that the
software does.  But this License is not limited to software manuals;
it can be used for any textual work, regardless of subject matter or
whether it is published as a printed book.  We recommend this License
principally for works whose purpose is instruction or reference.
</p>
</li><li> APPLICABILITY AND DEFINITIONS

<p>This License applies to any manual or other work, in any medium, that
contains a notice placed by the copyright holder saying it can be
distributed under the terms of this License.  Such a notice grants a
world-wide, royalty-free license, unlimited in duration, to use that
work under the conditions stated herein.  The &ldquo;Document&rdquo;, below,
refers to any such manual or work.  Any member of the public is a
licensee, and is addressed as &ldquo;you&rdquo;.  You accept the license if you
copy, modify or distribute the work in a way requiring permission
under copyright law.
</p>
<p>A &ldquo;Modified Version&rdquo; of the Document means any work containing the
Document or a portion of it, either copied verbatim, or with
modifications and/or translated into another language.
</p>
<p>A &ldquo;Secondary Section&rdquo; is a named appendix or a front-matter section
of the Document that deals exclusively with the relationship of the
publishers or authors of the Document to the Document&rsquo;s overall
subject (or to related matters) and contains nothing that could fall
directly within that overall subject.  (Thus, if the Document is in
part a textbook of mathematics, a Secondary Section may not explain
any mathematics.)  The relationship could be a matter of historical
connection with the subject or with related matters, or of legal,
commercial, philosophical, ethical or political position regarding
them.
</p>
<p>The &ldquo;Invariant Sections&rdquo; are certain Secondary Sections whose titles
are designated, as being those of Invariant Sections, in the notice
that says that the Document is released under this License.  If a
section does not fit the above definition of Secondary then it is not
allowed to be designated as Invariant.  The Document may contain zero
Invariant Sections.  If the Document does not identify any Invariant
Sections then there are none.
</p>
<p>The &ldquo;Cover Texts&rdquo; are certain short passages of text that are listed,
as Front-Cover Texts or Back-Cover Texts, in the notice that says that
the Document is released under this License.  A Front-Cover Text may
be at most 5 words, and a Back-Cover Text may be at most 25 words.
</p>
<p>A &ldquo;Transparent&rdquo; copy of the Document means a machine-readable copy,
represented in a format whose specification is available to the
general public, that is suitable for revising the document
straightforwardly with generic text editors or (for images composed of
pixels) generic paint programs or (for drawings) some widely available
drawing editor, and that is suitable for input to text formatters or
for automatic translation to a variety of formats suitable for input
to text formatters.  A copy made in an otherwise Transparent file
format whose markup, or absence of markup, has been arranged to thwart
or discourage subsequent modification by readers is not Transparent.
An image format is not Transparent if used for any substantial amount
of text.  A copy that is not &ldquo;Transparent&rdquo; is called &ldquo;Opaque&rdquo;.
</p>
<p>Examples of suitable formats for Transparent copies include plain
<small class="sc">ASCII</small> without markup, Texinfo input format, LaTeX input
format, <abbr class="acronym">SGML</abbr> or <abbr class="acronym">XML</abbr> using a publicly available
<abbr class="acronym">DTD</abbr>, and standard-conforming simple <abbr class="acronym">HTML</abbr>,
PostScript or <abbr class="acronym">PDF</abbr> designed for human modification.  Examples
of transparent image formats include <abbr class="acronym">PNG</abbr>, <abbr class="acronym">XCF</abbr> and
<abbr class="acronym">JPG</abbr>.  Opaque formats include proprietary formats that can be
read and edited only by proprietary word processors, <abbr class="acronym">SGML</abbr> or
<abbr class="acronym">XML</abbr> for which the <abbr class="acronym">DTD</abbr> and/or processing tools are
not generally available, and the machine-generated <abbr class="acronym">HTML</abbr>,
PostScript or <abbr class="acronym">PDF</abbr> produced by some word processors for
output purposes only.
</p>
<p>The &ldquo;Title Page&rdquo; means, for a printed book, the title page itself,
plus such following pages as are needed to hold, legibly, the material
this License requires to appear in the title page.  For works in
formats which do not have any title page as such, &ldquo;Title Page&rdquo; means
the text near the most prominent appearance of the work&rsquo;s title,
preceding the beginning of the body of the text.
</p>
<p>The &ldquo;publisher&rdquo; means any person or entity that distributes copies
of the Document to the public.
</p>
<p>A section &ldquo;Entitled XYZ&rdquo; means a named subunit of the Document whose
title either is precisely XYZ or contains XYZ in parentheses following
text that translates XYZ in another language.  (Here XYZ stands for a
specific section name mentioned below, such as &ldquo;Acknowledgements&rdquo;,
&ldquo;Dedications&rdquo;, &ldquo;Endorsements&rdquo;, or &ldquo;History&rdquo;.)  To &ldquo;Preserve the Title&rdquo;
of such a section when you modify the Document means that it remains a
section &ldquo;Entitled XYZ&rdquo; according to this definition.
</p>
<p>The Document may include Warranty Disclaimers next to the notice which
states that this License applies to the Document.  These Warranty
Disclaimers are considered to be included by reference in this
License, but only as regards disclaiming warranties: any other
implication that these Warranty Disclaimers may have is void and has
no effect on the meaning of this License.
</p>
</li><li> VERBATIM COPYING

<p>You may copy and distribute the Document in any medium, either
commercially or noncommercially, provided that this License, the
copyright notices, and the license notice saying this License applies
to the Document are reproduced in all copies, and that you add no other
conditions whatsoever to those of this License.  You may not use
technical measures to obstruct or control the reading or further
copying of the copies you make or distribute.  However, you may accept
compensation in exchange for copies.  If you distribute a large enough
number of copies you must also follow the conditions in section 3.
</p>
<p>You may also lend copies, under the same conditions stated above, and
you may publicly display copies.
</p>
</li><li> COPYING IN QUANTITY

<p>If you publish printed copies (or copies in media that commonly have
printed covers) of the Document, numbering more than 100, and the
Document&rsquo;s license notice requires Cover Texts, you must enclose the
copies in covers that carry, clearly and legibly, all these Cover
Texts: Front-Cover Texts on the front cover, and Back-Cover Texts on
the back cover.  Both covers must also clearly and legibly identify
you as the publisher of these copies.  The front cover must present
the full title with all words of the title equally prominent and
visible.  You may add other material on the covers in addition.
Copying with changes limited to the covers, as long as they preserve
the title of the Document and satisfy these conditions, can be treated
as verbatim copying in other respects.
</p>
<p>If the required texts for either cover are too voluminous to fit
legibly, you should put the first ones listed (as many as fit
reasonably) on the actual cover, and continue the rest onto adjacent
pages.
</p>
<p>If you publish or distribute Opaque copies of the Document numbering
more than 100, you must either include a machine-readable Transparent
copy along with each Opaque copy, or state in or with each Opaque copy
a computer-network location from which the general network-using
public has access to download using public-standard network protocols
a complete Transparent copy of the Document, free of added material.
If you use the latter option, you must take reasonably prudent steps,
when you begin distribution of Opaque copies in quantity, to ensure
that this Transparent copy will remain thus accessible at the stated
location until at least one year after the last time you distribute an
Opaque copy (directly or through your agents or retailers) of that
edition to the public.
</p>
<p>It is requested, but not required, that you contact the authors of the
Document well before redistributing any large number of copies, to give
them a chance to provide you with an updated version of the Document.
</p>
</li><li> MODIFICATIONS

<p>You may copy and distribute a Modified Version of the Document under
the conditions of sections 2 and 3 above, provided that you release
the Modified Version under precisely this License, with the Modified
Version filling the role of the Document, thus licensing distribution
and modification of the Modified Version to whoever possesses a copy
of it.  In addition, you must do these things in the Modified Version:
</p>
<ol class="enumerate" type="A" start="1">
<li> Use in the Title Page (and on the covers, if any) a title distinct
from that of the Document, and from those of previous versions
(which should, if there were any, be listed in the History section
of the Document).  You may use the same title as a previous version
if the original publisher of that version gives permission.

</li><li> List on the Title Page, as authors, one or more persons or entities
responsible for authorship of the modifications in the Modified
Version, together with at least five of the principal authors of the
Document (all of its principal authors, if it has fewer than five),
unless they release you from this requirement.

</li><li> State on the Title page the name of the publisher of the
Modified Version, as the publisher.

</li><li> Preserve all the copyright notices of the Document.

</li><li> Add an appropriate copyright notice for your modifications
adjacent to the other copyright notices.

</li><li> Include, immediately after the copyright notices, a license notice
giving the public permission to use the Modified Version under the
terms of this License, in the form shown in the Addendum below.

</li><li> Preserve in that license notice the full lists of Invariant Sections
and required Cover Texts given in the Document&rsquo;s license notice.

</li><li> Include an unaltered copy of this License.

</li><li> Preserve the section Entitled &ldquo;History&rdquo;, Preserve its Title, and add
to it an item stating at least the title, year, new authors, and
publisher of the Modified Version as given on the Title Page.  If
there is no section Entitled &ldquo;History&rdquo; in the Document, create one
stating the title, year, authors, and publisher of the Document as
given on its Title Page, then add an item describing the Modified
Version as stated in the previous sentence.

</li><li> Preserve the network location, if any, given in the Document for
public access to a Transparent copy of the Document, and likewise
the network locations given in the Document for previous versions
it was based on.  These may be placed in the &ldquo;History&rdquo; section.
You may omit a network location for a work that was published at
least four years before the Document itself, or if the original
publisher of the version it refers to gives permission.

</li><li> For any section Entitled &ldquo;Acknowledgements&rdquo; or &ldquo;Dedications&rdquo;, Preserve
the Title of the section, and preserve in the section all the
substance and tone of each of the contributor acknowledgements and/or
dedications given therein.

</li><li> Preserve all the Invariant Sections of the Document,
unaltered in their text and in their titles.  Section numbers
or the equivalent are not considered part of the section titles.

</li><li> Delete any section Entitled &ldquo;Endorsements&rdquo;.  Such a section
may not be included in the Modified Version.

</li><li> Do not retitle any existing section to be Entitled &ldquo;Endorsements&rdquo; or
to conflict in title with any Invariant Section.

</li><li> Preserve any Warranty Disclaimers.
</li></ol>

<p>If the Modified Version includes new front-matter sections or
appendices that qualify as Secondary Sections and contain no material
copied from the Document, you may at your option designate some or all
of these sections as invariant.  To do this, add their titles to the
list of Invariant Sections in the Modified Version&rsquo;s license notice.
These titles must be distinct from any other section titles.
</p>
<p>You may add a section Entitled &ldquo;Endorsements&rdquo;, provided it contains
nothing but endorsements of your Modified Version by various
parties&mdash;for example, statements of peer review or that the text has
been approved by an organization as the authoritative definition of a
standard.
</p>
<p>You may add a passage of up to five words as a Front-Cover Text, and a
passage of up to 25 words as a Back-Cover Text, to the end of the list
of Cover Texts in the Modified Version.  Only one passage of
Front-Cover Text and one of Back-Cover Text may be added by (or
through arrangements made by) any one entity.  If the Document already
includes a cover text for the same cover, previously added by you or
by arrangement made by the same entity you are acting on behalf of,
you may not add another; but you may replace the old one, on explicit
permission from the previous publisher that added the old one.
</p>
<p>The author(s) and publisher(s) of the Document do not by this License
give permission to use their names for publicity for or to assert or
imply endorsement of any Modified Version.
</p>
</li><li> COMBINING DOCUMENTS

<p>You may combine the Document with other documents released under this
License, under the terms defined in section 4 above for modified
versions, provided that you include in the combination all of the
Invariant Sections of all of the original documents, unmodified, and
list them all as Invariant Sections of your combined work in its
license notice, and that you preserve all their Warranty Disclaimers.
</p>
<p>The combined work need only contain one copy of this License, and
multiple identical Invariant Sections may be replaced with a single
copy.  If there are multiple Invariant Sections with the same name but
different contents, make the title of each such section unique by
adding at the end of it, in parentheses, the name of the original
author or publisher of that section if known, or else a unique number.
Make the same adjustment to the section titles in the list of
Invariant Sections in the license notice of the combined work.
</p>
<p>In the combination, you must combine any sections Entitled &ldquo;History&rdquo;
in the various original documents, forming one section Entitled
&ldquo;History&rdquo;; likewise combine any sections Entitled &ldquo;Acknowledgements&rdquo;,
and any sections Entitled &ldquo;Dedications&rdquo;.  You must delete all
sections Entitled &ldquo;Endorsements.&rdquo;
</p>
</li><li> COLLECTIONS OF DOCUMENTS

<p>You may make a collection consisting of the Document and other documents
released under this License, and replace the individual copies of this
License in the various documents with a single copy that is included in
the collection, provided that you follow the rules of this License for
verbatim copying of each of the documents in all other respects.
</p>
<p>You may extract a single document from such a collection, and distribute
it individually under this License, provided you insert a copy of this
License into the extracted document, and follow this License in all
other respects regarding verbatim copying of that document.
</p>
</li><li> AGGREGATION WITH INDEPENDENT WORKS

<p>A compilation of the Document or its derivatives with other separate
and independent documents or works, in or on a volume of a storage or
distribution medium, is called an &ldquo;aggregate&rdquo; if the copyright
resulting from the compilation is not used to limit the legal rights
of the compilation&rsquo;s users beyond what the individual works permit.
When the Document is included in an aggregate, this License does not
apply to the other works in the aggregate which are not themselves
derivative works of the Document.
</p>
<p>If the Cover Text requirement of section 3 is applicable to these
copies of the Document, then if the Document is less than one half of
the entire aggregate, the Document&rsquo;s Cover Texts may be placed on
covers that bracket the Document within the aggregate, or the
electronic equivalent of covers if the Document is in electronic form.
Otherwise they must appear on printed covers that bracket the whole
aggregate.
</p>
</li><li> TRANSLATION

<p>Translation is considered a kind of modification, so you may
distribute translations of the Document under the terms of section 4.
Replacing Invariant Sections with translations requires special
permission from their copyright holders, but you may include
translations of some or all Invariant Sections in addition to the
original versions of these Invariant Sections.  You may include a
translation of this License, and all the license notices in the
Document, and any Warranty Disclaimers, provided that you also include
the original English version of this License and the original versions
of those notices and disclaimers.  In case of a disagreement between
the translation and the original version of this License or a notice
or disclaimer, the original version will prevail.
</p>
<p>If a section in the Document is Entitled &ldquo;Acknowledgements&rdquo;,
&ldquo;Dedications&rdquo;, or &ldquo;History&rdquo;, the requirement (section 4) to Preserve
its Title (section 1) will typically require changing the actual
title.
</p>
</li><li> TERMINATION

<p>You may not copy, modify, sublicense, or distribute the Document
except as expressly provided under this License.  Any attempt
otherwise to copy, modify, sublicense, or distribute it is void, and
will automatically terminate your rights under this License.
</p>
<p>However, if you cease all violation of this License, then your license
from a particular copyright holder is reinstated (a) provisionally,
unless and until the copyright holder explicitly and finally
terminates your license, and (b) permanently, if the copyright holder
fails to notify you of the violation by some reasonable means prior to
60 days after the cessation.
</p>
<p>Moreover, your license from a particular copyright holder is
reinstated permanently if the copyright holder notifies you of the
violation by some reasonable means, this is the first time you have
received notice of violation of this License (for any work) from that
copyright holder, and you cure the violation prior to 30 days after
your receipt of the notice.
</p>
<p>Termination of your rights under this section does not terminate the
licenses of parties who have received copies or rights from you under
this License.  If your rights have been terminated and not permanently
reinstated, receipt of a copy of some or all of the same material does
not give you any rights to use it.
</p>
</li><li> FUTURE REVISIONS OF THIS LICENSE

<p>The Free Software Foundation may publish new, revised versions
of the GNU Free Documentation License from time to time.  Such new
versions will be similar in spirit to the present version, but may
differ in detail to address new problems or concerns.  See
<a class="uref" href="http://www.gnu.org/copyleft/">http://www.gnu.org/copyleft/</a>.
</p>
<p>Each version of the License is given a distinguishing version number.
If the Document specifies that a particular numbered version of this
License &ldquo;or any later version&rdquo; applies to it, you have the option of
following the terms and conditions either of that specified version or
of any later version that has been published (not as a draft) by the
Free Software Foundation.  If the Document does not specify a version
number of this License, you may choose any version ever published (not
as a draft) by the Free Software Foundation.  If the Document
specifies that a proxy can decide which future versions of this
License can be used, that proxy&rsquo;s public statement of acceptance of a
version permanently authorizes you to choose that version for the
Document.
</p>
</li><li> RELICENSING

<p>&ldquo;Massive Multiauthor Collaboration Site&rdquo; (or &ldquo;MMC Site&rdquo;) means any
World Wide Web server that publishes copyrightable works and also
provides prominent facilities for anybody to edit those works.  A
public wiki that anybody can edit is an example of such a server.  A
&ldquo;Massive Multiauthor Collaboration&rdquo; (or &ldquo;MMC&rdquo;) contained in the
site means any set of copyrightable works thus published on the MMC
site.
</p>
<p>&ldquo;CC-BY-SA&rdquo; means the Creative Commons Attribution-Share Alike 3.0
license published by Creative Commons Corporation, a not-for-profit
corporation with a principal place of business in San Francisco,
California, as well as future copyleft versions of that license
published by that same organization.
</p>
<p>&ldquo;Incorporate&rdquo; means to publish or republish a Document, in whole or
in part, as part of another Document.
</p>
<p>An MMC is &ldquo;eligible for relicensing&rdquo; if it is licensed under this
License, and if all works that were first published under this License
somewhere other than this MMC, and subsequently incorporated in whole
or in part into the MMC, (1) had no cover texts or invariant sections,
and (2) were thus incorporated prior to November 1, 2008.
</p>
<p>The operator of an MMC Site may republish an MMC contained in the site
under CC-BY-SA on the same site at any time before August 1, 2009,
provided the MMC is eligible for relicensing.
</p>
</li></ol>

<h3 class="heading" id="ADDENDUM_003a-How-to-use-this-License-for-your-documents"><span>ADDENDUM: How to use this License for your documents<a class="copiable-link" href="#ADDENDUM_003a-How-to-use-this-License-for-your-documents"> &para;</a></span></h3>

<p>To use this License in a document you have written, include a copy of
the License in the document and put the following copyright and
license notices just after the title page:
</p>
<div class="example smallexample">
<div class="group"><pre class="example-preformatted">  Copyright (C)  <var class="var">year</var>  <var class="var">your name</var>.
  Permission is granted to copy, distribute and/or modify this document
  under the terms of the GNU Free Documentation License, Version 1.3
  or any later version published by the Free Software Foundation;
  with no Invariant Sections, no Front-Cover Texts, and no Back-Cover
  Texts.  A copy of the license is included in the section entitled ``GNU
  Free Documentation License''.
</pre></div></div>

<p>If you have Invariant Sections, Front-Cover Texts and Back-Cover Texts,
replace the &ldquo;with&hellip;Texts.&rdquo; line with this:
</p>
<div class="example smallexample">
<div class="group"><pre class="example-preformatted">    with the Invariant Sections being <var class="var">list their titles</var>, with
    the Front-Cover Texts being <var class="var">list</var>, and with the Back-Cover Texts
    being <var class="var">list</var>.
</pre></div></div>

<p>If you have Invariant Sections without Cover Texts, or some other
combination of the three, merge those two alternatives to suit the
situation.
</p>
<p>If your document contains nontrivial examples of program code, we
recommend releasing these examples in parallel under your choice of
free software license, such as the GNU General Public License,
to permit their use in free software.
</p>


<hr>
</div>
<div class="unnumbered-level-extent" id="Concept-Index">
<div class="nav-panel">
<p>
Next: <a href="#Function-and-Variable-Index" accesskey="n" rel="next">Function and Variable Index</a>, Previous: <a href="#GNU-Free-Documentation-License" accesskey="p" rel="prev">GNU Free Documentation License</a>, Up: <a href="#Top" accesskey="u" rel="up">GNU Readline Library</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h2 class="unnumbered" id="Concept-Index-1"><span>Concept Index<a class="copiable-link" href="#Concept-Index-1"> &para;</a></span></h2>
<div class="printindex cp-printindex">
<table class="cp-letters-header-printindex"><tr><th>Jump to: &nbsp; </th><td><a class="summary-letter-printindex" href="#Concept-Index_cp_letter-A"><b>A</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-C"><b>C</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-E"><b>E</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-I"><b>I</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-K"><b>K</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-N"><b>N</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-R"><b>R</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-V"><b>V</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-Y"><b>Y</b></a>
 &nbsp; 
</td></tr></table>
<table class="cp-entries-printindex" border="0">
<tr><td></td><th class="entries-header-printindex">Index Entry</th><th class="sections-header-printindex">Section</th></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Concept-Index_cp_letter-A">A</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-application_002dspecific-completion-functions">application-specific completion functions</a></td><td class="printindex-index-section"><a href="#Custom-Completers">Custom Completers</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Concept-Index_cp_letter-C">C</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-command-editing">command editing</a></td><td class="printindex-index-section"><a href="#Readline-Bare-Essentials">Readline Bare Essentials</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Concept-Index_cp_letter-E">E</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-editing-command-lines">editing command lines</a></td><td class="printindex-index-section"><a href="#Readline-Bare-Essentials">Readline Bare Essentials</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Concept-Index_cp_letter-I">I</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-initialization-file_002c-readline">initialization file, readline</a></td><td class="printindex-index-section"><a href="#Readline-Init-File">Readline Init File</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-interaction_002c-readline">interaction, readline</a></td><td class="printindex-index-section"><a href="#Readline-Interaction">Readline Interaction</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Concept-Index_cp_letter-K">K</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-kill-ring">kill ring</a></td><td class="printindex-index-section"><a href="#Readline-Killing-Commands">Readline Killing Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-killing-text">killing text</a></td><td class="printindex-index-section"><a href="#Readline-Killing-Commands">Readline Killing Commands</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Concept-Index_cp_letter-N">N</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-notation_002c-readline">notation, readline</a></td><td class="printindex-index-section"><a href="#Readline-Bare-Essentials">Readline Bare Essentials</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Concept-Index_cp_letter-R">R</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-readline_002c-function">readline, function</a></td><td class="printindex-index-section"><a href="#Basic-Behavior">Basic Behavior</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Concept-Index_cp_letter-V">V</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-variables_002c-readline">variables, readline</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Concept-Index_cp_letter-Y">Y</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-yanking-text">yanking text</a></td><td class="printindex-index-section"><a href="#Readline-Killing-Commands">Readline Killing Commands</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
</table>
<table class="cp-letters-footer-printindex"><tr><th>Jump to: &nbsp; </th><td><a class="summary-letter-printindex" href="#Concept-Index_cp_letter-A"><b>A</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-C"><b>C</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-E"><b>E</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-I"><b>I</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-K"><b>K</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-N"><b>N</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-R"><b>R</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-V"><b>V</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Concept-Index_cp_letter-Y"><b>Y</b></a>
 &nbsp; 
</td></tr></table>
</div>

<hr>
</div>
<div class="unnumbered-level-extent" id="Function-and-Variable-Index">
<div class="nav-panel">
<p>
Previous: <a href="#Concept-Index" accesskey="p" rel="prev">Concept Index</a>, Up: <a href="#Top" accesskey="u" rel="up">GNU Readline Library</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<h2 class="unnumbered" id="Function-and-Variable-Index-1"><span>Function and Variable Index<a class="copiable-link" href="#Function-and-Variable-Index-1"> &para;</a></span></h2>
<div class="printindex fn-printindex">
<table class="fn-letters-header-printindex"><tr><th>Jump to: &nbsp; </th><td><a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_symbol-1"><b>_</b></a>
 &nbsp; 
<br>
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-A"><b>A</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-B"><b>B</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-C"><b>C</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-D"><b>D</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-E"><b>E</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-F"><b>F</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-H"><b>H</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-I"><b>I</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-K"><b>K</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-M"><b>M</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-N"><b>N</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-O"><b>O</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-P"><b>P</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-Q"><b>Q</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-R"><b>R</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-S"><b>S</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-T"><b>T</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-U"><b>U</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-V"><b>V</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-Y"><b>Y</b></a>
 &nbsp; 
</td></tr></table>
<table class="fn-entries-printindex" border="0">
<tr><td></td><th class="entries-header-printindex">Index Entry</th><th class="sections-header-printindex">Section</th></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_symbol-1">_</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-_005frl_005fdigit_005fp"><code>_rl_digit_p</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-_005frl_005fdigit_005fvalue"><code>_rl_digit_value</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-_005frl_005flowercase_005fp"><code>_rl_lowercase_p</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-_005frl_005fto_005flower"><code>_rl_to_lower</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-_005frl_005fto_005fupper"><code>_rl_to_upper</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-_005frl_005fuppercase_005fp"><code>_rl_uppercase_p</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-A">A</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-abort-_0028C_002dg_0029"><code>abort (C-g)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-accept_002dline-_0028Newline-or-Return_0029"><code>accept-line (Newline or Return)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-active_002dregion_002dend_002dcolor">active-region-end-color</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-active_002dregion_002dstart_002dcolor">active-region-start-color</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-B">B</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-backward_002dchar-_0028C_002db_0029"><code>backward-char (C-b)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Moving">Commands For Moving</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-backward_002ddelete_002dchar-_0028Rubout_0029"><code>backward-delete-char (Rubout)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-backward_002dkill_002dline-_0028C_002dx-Rubout_0029"><code>backward-kill-line (C-x Rubout)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-backward_002dkill_002dword-_0028M_002dDEL_0029"><code>backward-kill-word (M-<kbd class="key">DEL</kbd>)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-backward_002dword-_0028M_002db_0029"><code>backward-word (M-b)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Moving">Commands For Moving</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-beginning_002dof_002dhistory-_0028M_002d_003c_0029"><code>beginning-of-history (M-&lt;)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-beginning_002dof_002dline-_0028C_002da_0029"><code>beginning-of-line (C-a)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Moving">Commands For Moving</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-bell_002dstyle">bell-style</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-bind_002dtty_002dspecial_002dchars">bind-tty-special-chars</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-blink_002dmatching_002dparen">blink-matching-paren</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-bracketed_002dpaste_002dbegin-_0028_0029"><code>bracketed-paste-begin ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-C">C</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-call_002dlast_002dkbd_002dmacro-_0028C_002dx-e_0029"><code>call-last-kbd-macro (C-x e)</code></a></td><td class="printindex-index-section"><a href="#Keyboard-Macros">Keyboard Macros</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-capitalize_002dword-_0028M_002dc_0029"><code>capitalize-word (M-c)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-character_002dsearch-_0028C_002d_005d_0029"><code>character-search (C-])</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-character_002dsearch_002dbackward-_0028M_002dC_002d_005d_0029"><code>character-search-backward (M-C-])</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-clear_002ddisplay-_0028M_002dC_002dl_0029"><code>clear-display (M-C-l)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Moving">Commands For Moving</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-clear_002dscreen-_0028C_002dl_0029"><code>clear-screen (C-l)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Moving">Commands For Moving</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-colored_002dcompletion_002dprefix">colored-completion-prefix</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-colored_002dstats">colored-stats</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-comment_002dbegin">comment-begin</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-complete-_0028TAB_0029"><code>complete (<kbd class="key">TAB</kbd>)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Completion">Commands For Completion</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-completion_002ddisplay_002dwidth">completion-display-width</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-completion_002dignore_002dcase">completion-ignore-case</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-completion_002dmap_002dcase">completion-map-case</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-completion_002dprefix_002ddisplay_002dlength">completion-prefix-display-length</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-completion_002dquery_002ditems">completion-query-items</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-convert_002dmeta">convert-meta</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-copy_002dbackward_002dword-_0028_0029"><code>copy-backward-word ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-copy_002dforward_002dword-_0028_0029"><code>copy-forward-word ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-copy_002dregion_002das_002dkill-_0028_0029"><code>copy-region-as-kill ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-D">D</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-delete_002dchar-_0028C_002dd_0029"><code>delete-char (C-d)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-delete_002dchar_002dor_002dlist-_0028_0029"><code>delete-char-or-list ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Completion">Commands For Completion</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-delete_002dhorizontal_002dspace-_0028_0029"><code>delete-horizontal-space ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-digit_002dargument-_0028M_002d0_002c-M_002d1_002c-_2026-M_002d_002d_0029"><code>digit-argument (<kbd class="kbd">M-0</kbd>, <kbd class="kbd">M-1</kbd>, &hellip; <kbd class="kbd">M--</kbd>)</code></a></td><td class="printindex-index-section"><a href="#Numeric-Arguments">Numeric Arguments</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-disable_002dcompletion">disable-completion</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-do_002dlowercase_002dversion-_0028M_002dA_002c-M_002dB_002c-M_002dx_002c-_2026_0029"><code>do-lowercase-version (M-A, M-B, M-<var class="var">x</var>, &hellip;)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-downcase_002dword-_0028M_002dl_0029"><code>downcase-word (M-l)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-dump_002dfunctions-_0028_0029"><code>dump-functions ()</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-dump_002dmacros-_0028_0029"><code>dump-macros ()</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-dump_002dvariables-_0028_0029"><code>dump-variables ()</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-E">E</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-echo_002dcontrol_002dcharacters">echo-control-characters</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-editing_002dmode">editing-mode</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-emacs_002dediting_002dmode-_0028C_002de_0029"><code>emacs-editing-mode (C-e)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-emacs_002dmode_002dstring">emacs-mode-string</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-enable_002dactive_002dregion-The">enable-active-region The</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-enable_002dbracketed_002dpaste">enable-bracketed-paste</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-enable_002dkeypad">enable-keypad</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-enable_002dmeta_002dkey">enable-meta-key</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-end_002dkbd_002dmacro-_0028C_002dx-_0029_0029"><code>end-kbd-macro (C-x ))</code></a></td><td class="printindex-index-section"><a href="#Keyboard-Macros">Keyboard Macros</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-end_002dof_002dfile-_0028usually-C_002dd_0029"><code><i class="i">end-of-file</i> (usually C-d)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-end_002dof_002dhistory-_0028M_002d_003e_0029"><code>end-of-history (M-&gt;)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-end_002dof_002dline-_0028C_002de_0029"><code>end-of-line (C-e)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Moving">Commands For Moving</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-exchange_002dpoint_002dand_002dmark-_0028C_002dx-C_002dx_0029"><code>exchange-point-and-mark (C-x C-x)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-execute_002dnamed_002dcommand-_0028M_002dx_0029"><code>execute-named-command (M-x)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-expand_002dtilde">expand-tilde</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-export_002dcompletions-_0028_0029"><code>export-completions ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Completion">Commands For Completion</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-F">F</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-fetch_002dhistory-_0028_0029"><code>fetch-history ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-force_002dmeta_002dprefix">force-meta-prefix</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-forward_002dbackward_002ddelete_002dchar-_0028_0029"><code>forward-backward-delete-char ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-forward_002dchar-_0028C_002df_0029"><code>forward-char (C-f)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Moving">Commands For Moving</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-forward_002dsearch_002dhistory-_0028C_002ds_0029"><code>forward-search-history (C-s)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-forward_002dword-_0028M_002df_0029"><code>forward-word (M-f)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Moving">Commands For Moving</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-H">H</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-history_002dpreserve_002dpoint">history-preserve-point</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-history_002dsearch_002dbackward-_0028_0029"><code>history-search-backward ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-history_002dsearch_002dforward-_0028_0029"><code>history-search-forward ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-history_002dsize">history-size</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-history_002dsubstring_002dsearch_002dbackward-_0028_0029"><code>history-substring-search-backward ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-history_002dsubstring_002dsearch_002dforward-_0028_0029"><code>history-substring-search-forward ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-horizontal_002dscroll_002dmode">horizontal-scroll-mode</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-I">I</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-input_002dmeta">input-meta</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-insert_002dcomment-_0028M_002d_0023_0029"><code>insert-comment (M-#)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-insert_002dcompletions-_0028M_002d_002a_0029"><code>insert-completions (M-*)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Completion">Commands For Completion</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-isearch_002dterminators">isearch-terminators</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-K">K</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-keymap">keymap</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-kill_002dline-_0028C_002dk_0029"><code>kill-line (C-k)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-kill_002dregion-_0028_0029"><code>kill-region ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-kill_002dwhole_002dline-_0028_0029"><code>kill-whole-line ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-kill_002dword-_0028M_002dd_0029"><code>kill-word (M-d)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-M">M</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-mark_002dmodified_002dlines">mark-modified-lines</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-mark_002dsymlinked_002ddirectories">mark-symlinked-directories</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-match_002dhidden_002dfiles">match-hidden-files</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-menu_002dcomplete-_0028_0029"><code>menu-complete ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Completion">Commands For Completion</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-menu_002dcomplete_002dbackward-_0028_0029"><code>menu-complete-backward ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Completion">Commands For Completion</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-menu_002dcomplete_002ddisplay_002dprefix">menu-complete-display-prefix</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-meta_002dflag">meta-flag</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-N">N</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-next_002dhistory-_0028C_002dn_0029"><code>next-history (C-n)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-next_002dscreen_002dline-_0028_0029"><code>next-screen-line ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Moving">Commands For Moving</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-non_002dincremental_002dforward_002dsearch_002dhistory-_0028M_002dn_0029"><code>non-incremental-forward-search-history (M-n)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-non_002dincremental_002dreverse_002dsearch_002dhistory-_0028M_002dp_0029"><code>non-incremental-reverse-search-history (M-p)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-O">O</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-operate_002dand_002dget_002dnext-_0028C_002do_0029"><code>operate-and-get-next (C-o)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-output_002dmeta">output-meta</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-overwrite_002dmode-_0028_0029"><code>overwrite-mode ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-P">P</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-page_002dcompletions">page-completions</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-possible_002dcompletions-_0028M_002d_003f_0029"><code>possible-completions (M-?)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Completion">Commands For Completion</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-prefix_002dmeta-_0028ESC_0029"><code>prefix-meta (<kbd class="key">ESC</kbd>)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-previous_002dhistory-_0028C_002dp_0029"><code>previous-history (C-p)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-previous_002dscreen_002dline-_0028_0029"><code>previous-screen-line ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Moving">Commands For Moving</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-print_002dlast_002dkbd_002dmacro-_0028_0029"><code>print-last-kbd-macro ()</code></a></td><td class="printindex-index-section"><a href="#Keyboard-Macros">Keyboard Macros</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-Q">Q</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-quoted_002dinsert-_0028C_002dq-or-C_002dv_0029"><code>quoted-insert (C-q or C-v)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-R">R</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-re_002dread_002dinit_002dfile-_0028C_002dx-C_002dr_0029"><code>re-read-init-file (C-x C-r)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-readline"><code>readline</code></a></td><td class="printindex-index-section"><a href="#Basic-Behavior">Basic Behavior</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-redraw_002dcurrent_002dline-_0028_0029"><code>redraw-current-line ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Moving">Commands For Moving</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-reverse_002dsearch_002dhistory-_0028C_002dr_0029"><code>reverse-search-history (C-r)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-revert_002dall_002dat_002dnewline">revert-all-at-newline</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-revert_002dline-_0028M_002dr_0029"><code>revert-line (M-r)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005factivate_005fmark"><code>rl_activate_mark</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fadd_005fdefun"><code>rl_add_defun</code></a></td><td class="printindex-index-section"><a href="#Function-Naming">Function Naming</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fadd_005ffunmap_005fentry"><code>rl_add_funmap_entry</code></a></td><td class="printindex-index-section"><a href="#Associating-Function-Names-and-Bindings">Associating Function Names and Bindings</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fadd_005fundo"><code>rl_add_undo</code></a></td><td class="printindex-index-section"><a href="#Allowing-Undoing">Allowing Undoing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005falphabetic"><code>rl_alphabetic</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005falready_005fprompted">rl_already_prompted</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fattempted_005fcompletion_005ffunction">rl_attempted_completion_function</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fattempted_005fcompletion_005fover">rl_attempted_completion_over</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbasic_005fquote_005fcharacters">rl_basic_quote_characters</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbasic_005fword_005fbreak_005fcharacters">rl_basic_word_break_characters</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbegin_005fundo_005fgroup"><code>rl_begin_undo_group</code></a></td><td class="printindex-index-section"><a href="#Allowing-Undoing">Allowing Undoing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbind_005fkey"><code>rl_bind_key</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbind_005fkey_005fif_005funbound"><code>rl_bind_key_if_unbound</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbind_005fkey_005fif_005funbound_005fin_005fmap"><code>rl_bind_key_if_unbound_in_map</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbind_005fkey_005fin_005fmap"><code>rl_bind_key_in_map</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbind_005fkeyseq"><code>rl_bind_keyseq</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbind_005fkeyseq_005fif_005funbound"><code>rl_bind_keyseq_if_unbound</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbind_005fkeyseq_005fif_005funbound_005fin_005fmap"><code>rl_bind_keyseq_if_unbound_in_map</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbind_005fkeyseq_005fin_005fmap"><code>rl_bind_keyseq_in_map</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fbinding_005fkeymap">rl_binding_keymap</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcallback_005fhandler_005finstall"><code>rl_callback_handler_install</code></a></td><td class="printindex-index-section"><a href="#Alternate-Interface">Alternate Interface</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcallback_005fhandler_005fremove"><code>rl_callback_handler_remove</code></a></td><td class="printindex-index-section"><a href="#Alternate-Interface">Alternate Interface</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcallback_005fread_005fchar"><code>rl_callback_read_char</code></a></td><td class="printindex-index-section"><a href="#Alternate-Interface">Alternate Interface</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcallback_005fsigcleanup"><code>rl_callback_sigcleanup</code></a></td><td class="printindex-index-section"><a href="#Alternate-Interface">Alternate Interface</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcatch_005fsignals">rl_catch_signals</a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcatch_005fsigwinch">rl_catch_sigwinch</a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fchange_005fenvironment">rl_change_environment</a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fchar_005fis_005fquoted_005fp">rl_char_is_quoted_p</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcheck_005fsignals"><code>rl_check_signals</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcleanup_005fafter_005fsignal"><code>rl_cleanup_after_signal</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fclear_005fhistory"><code>rl_clear_history</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fclear_005fmessage"><code>rl_clear_message</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fclear_005fpending_005finput"><code>rl_clear_pending_input</code></a></td><td class="printindex-index-section"><a href="#Character-Input">Character Input</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fclear_005fsignals"><code>rl_clear_signals</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fclear_005fvisible_005fline"><code>rl_clear_visible_line</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcomplete"><code>rl_complete</code></a></td><td class="printindex-index-section"><a href="#How-Completing-Works">How Completing Works</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcomplete-1"><code>rl_complete</code></a></td><td class="printindex-index-section"><a href="#Completion-Functions">Completion Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcomplete_005finternal"><code>rl_complete_internal</code></a></td><td class="printindex-index-section"><a href="#Completion-Functions">Completion Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompleter_005fquote_005fcharacters">rl_completer_quote_characters</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompleter_005fword_005fbreak_005fcharacters">rl_completer_word_break_characters</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fappend_005fcharacter">rl_completion_append_character</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fdisplay_005fmatches_005fhook">rl_completion_display_matches_hook</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fentry_005ffunction">rl_completion_entry_function</a></td><td class="printindex-index-section"><a href="#How-Completing-Works">How Completing Works</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fentry_005ffunction-1">rl_completion_entry_function</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005ffound_005fquote">rl_completion_found_quote</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005finvoking_005fkey">rl_completion_invoking_key</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fmark_005fsymlink_005fdirs">rl_completion_mark_symlink_dirs</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fmatches"><code>rl_completion_matches</code></a></td><td class="printindex-index-section"><a href="#Completion-Functions">Completion Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fmode"><code>rl_completion_mode</code></a></td><td class="printindex-index-section"><a href="#Completion-Functions">Completion Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fquery_005fitems">rl_completion_query_items</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fquote_005fcharacter">rl_completion_quote_character</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005frewrite_005fhook">rl_completion_rewrite_hook</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fsuppress_005fappend">rl_completion_suppress_append</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fsuppress_005fquote">rl_completion_suppress_quote</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005ftype">rl_completion_type</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcompletion_005fword_005fbreak_005fhook">rl_completion_word_break_hook</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcopy_005fkeymap"><code>rl_copy_keymap</code></a></td><td class="printindex-index-section"><a href="#Keymaps">Keymaps</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcopy_005ftext"><code>rl_copy_text</code></a></td><td class="printindex-index-section"><a href="#Modifying-Text">Modifying Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fcrlf"><code>rl_crlf</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdeactivate_005fmark"><code>rl_deactivate_mark</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdelete_005ftext"><code>rl_delete_text</code></a></td><td class="printindex-index-section"><a href="#Modifying-Text">Modifying Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdeprep_005fterm_005ffunction">rl_deprep_term_function</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdeprep_005fterminal"><code>rl_deprep_terminal</code></a></td><td class="printindex-index-section"><a href="#Terminal-Management">Terminal Management</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fding"><code>rl_ding</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdirectory_005fcompletion_005fhook">rl_directory_completion_hook</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdirectory_005frewrite_005fhook_003b">rl_directory_rewrite_hook;</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdiscard_005fkeymap"><code>rl_discard_keymap</code></a></td><td class="printindex-index-section"><a href="#Keymaps">Keymaps</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdispatching">rl_dispatching</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdisplay_005fmatch_005flist"><code>rl_display_match_list</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdisplay_005fprompt">rl_display_prompt</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdo_005fundo"><code>rl_do_undo</code></a></td><td class="printindex-index-section"><a href="#Allowing-Undoing">Allowing Undoing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fdone">rl_done</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fecho_005fsignal_005fchar"><code>rl_echo_signal_char</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fediting_005fmode">rl_editing_mode</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fempty_005fkeymap"><code>rl_empty_keymap</code></a></td><td class="printindex-index-section"><a href="#Keymaps">Keymaps</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fend">rl_end</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fend_005fundo_005fgroup"><code>rl_end_undo_group</code></a></td><td class="printindex-index-section"><a href="#Allowing-Undoing">Allowing Undoing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005feof_005ffound">rl_eof_found</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ferase_005fempty_005fline">rl_erase_empty_line</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fevent_005fhook">rl_event_hook</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fexecute_005fnext"><code>rl_execute_next</code></a></td><td class="printindex-index-section"><a href="#Character-Input">Character Input</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fexecuting_005fkey">rl_executing_key</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fexecuting_005fkeymap">rl_executing_keymap</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fexecuting_005fkeyseq">rl_executing_keyseq</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fexecuting_005fmacro">rl_executing_macro</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fexpand_005fprompt"><code>rl_expand_prompt</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fexplicit_005farg">rl_explicit_arg</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fextend_005fline_005fbuffer"><code>rl_extend_line_buffer</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffilename_005fcompletion_005fdesired">rl_filename_completion_desired</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffilename_005fcompletion_005ffunction"><code>rl_filename_completion_function</code></a></td><td class="printindex-index-section"><a href="#Completion-Functions">Completion Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffilename_005fdequoting_005ffunction">rl_filename_dequoting_function</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffilename_005fquote_005fcharacters">rl_filename_quote_characters</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffilename_005fquoting_005fdesired">rl_filename_quoting_desired</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffilename_005fquoting_005ffunction">rl_filename_quoting_function</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffilename_005frewrite_005fhook">rl_filename_rewrite_hook</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffilename_005fstat_005fhook">rl_filename_stat_hook</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fforced_005fupdate_005fdisplay"><code>rl_forced_update_display</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffree"><code>rl_free</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffree_005fkeymap"><code>rl_free_keymap</code></a></td><td class="printindex-index-section"><a href="#Keymaps">Keymaps</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffree_005fline_005fstate"><code>rl_free_line_state</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffree_005fundo_005flist"><code>rl_free_undo_list</code></a></td><td class="printindex-index-section"><a href="#Allowing-Undoing">Allowing Undoing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffull_005fquoting_005fdesired">rl_full_quoting_desired</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffunction_005fdumper"><code>rl_function_dumper</code></a></td><td class="printindex-index-section"><a href="#Associating-Function-Names-and-Bindings">Associating Function Names and Bindings</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffunction_005fof_005fkeyseq"><code>rl_function_of_keyseq</code></a></td><td class="printindex-index-section"><a href="#Associating-Function-Names-and-Bindings">Associating Function Names and Bindings</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffunction_005fof_005fkeyseq_005flen"><code>rl_function_of_keyseq_len</code></a></td><td class="printindex-index-section"><a href="#Associating-Function-Names-and-Bindings">Associating Function Names and Bindings</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ffunmap_005fnames"><code>rl_funmap_names</code></a></td><td class="printindex-index-section"><a href="#Associating-Function-Names-and-Bindings">Associating Function Names and Bindings</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fgeneric_005fbind"><code>rl_generic_bind</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fget_005fkeymap"><code>rl_get_keymap</code></a></td><td class="printindex-index-section"><a href="#Keymaps">Keymaps</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fget_005fkeymap_005fby_005fname"><code>rl_get_keymap_by_name</code></a></td><td class="printindex-index-section"><a href="#Keymaps">Keymaps</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fget_005fkeymap_005fname"><code>rl_get_keymap_name</code></a></td><td class="printindex-index-section"><a href="#Keymaps">Keymaps</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fget_005fscreen_005fsize"><code>rl_get_screen_size</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fget_005ftermcap"><code>rl_get_termcap</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fgetc"><code>rl_getc</code></a></td><td class="printindex-index-section"><a href="#Character-Input">Character Input</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fgetc_005ffunction">rl_getc_function</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fgnu_005freadline_005fp">rl_gnu_readline_p</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fignore_005fcompletion_005fduplicates">rl_ignore_completion_duplicates</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fignore_005fsome_005fcompletions_005ffunction">rl_ignore_some_completions_function</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005finhibit_005fcompletion">rl_inhibit_completion</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005finitialize"><code>rl_initialize</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005finput_005favailable_005fhook">rl_input_available_hook</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005finsert_005fcompletions"><code>rl_insert_completions</code></a></td><td class="printindex-index-section"><a href="#Completion-Functions">Completion Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005finsert_005ftext"><code>rl_insert_text</code></a></td><td class="printindex-index-section"><a href="#Modifying-Text">Modifying Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005finstream">rl_instream</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005finvoking_005fkeyseqs"><code>rl_invoking_keyseqs</code></a></td><td class="printindex-index-section"><a href="#Associating-Function-Names-and-Bindings">Associating Function Names and Bindings</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005finvoking_005fkeyseqs_005fin_005fmap"><code>rl_invoking_keyseqs_in_map</code></a></td><td class="printindex-index-section"><a href="#Associating-Function-Names-and-Bindings">Associating Function Names and Bindings</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fkeep_005fmark_005factive"><code>rl_keep_mark_active</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fkey_005fsequence_005flength">rl_key_sequence_length</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fkill_005ftext"><code>rl_kill_text</code></a></td><td class="printindex-index-section"><a href="#Modifying-Text">Modifying Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005flast_005ffunc">rl_last_func</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005flibrary_005fversion">rl_library_version</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fline_005fbuffer">rl_line_buffer</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005flist_005ffunmap_005fnames"><code>rl_list_funmap_names</code></a></td><td class="printindex-index-section"><a href="#Associating-Function-Names-and-Bindings">Associating Function Names and Bindings</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fmacro_005fbind"><code>rl_macro_bind</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fmacro_005fdisplay_005fhook">rl_macro_display_hook</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fmacro_005fdumper"><code>rl_macro_dumper</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fmake_005fbare_005fkeymap"><code>rl_make_bare_keymap</code></a></td><td class="printindex-index-section"><a href="#Keymaps">Keymaps</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fmake_005fkeymap"><code>rl_make_keymap</code></a></td><td class="printindex-index-section"><a href="#Keymaps">Keymaps</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fmark">rl_mark</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fmark_005factive_005fp"><code>rl_mark_active_p</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fmessage"><code>rl_message</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fmodifying"><code>rl_modifying</code></a></td><td class="printindex-index-section"><a href="#Allowing-Undoing">Allowing Undoing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fnamed_005ffunction"><code>rl_named_function</code></a></td><td class="printindex-index-section"><a href="#Associating-Function-Names-and-Bindings">Associating Function Names and Bindings</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fnum_005fchars_005fto_005fread">rl_num_chars_to_read</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fnumeric_005farg">rl_numeric_arg</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fon_005fnew_005fline"><code>rl_on_new_line</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fon_005fnew_005fline_005fwith_005fprompt"><code>rl_on_new_line_with_prompt</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005foutstream">rl_outstream</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fparse_005fand_005fbind"><code>rl_parse_and_bind</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fpending_005finput">rl_pending_input</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fpending_005fsignal"><code>rl_pending_signal</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fpersistent_005fsignal_005fhandlers">rl_persistent_signal_handlers</a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fpoint">rl_point</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fpossible_005fcompletions"><code>rl_possible_completions</code></a></td><td class="printindex-index-section"><a href="#Completion-Functions">Completion Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fpre_005finput_005fhook">rl_pre_input_hook</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fprefer_005fenv_005fwinsize">rl_prefer_env_winsize</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fprep_005fterm_005ffunction">rl_prep_term_function</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fprep_005fterminal"><code>rl_prep_terminal</code></a></td><td class="printindex-index-section"><a href="#Terminal-Management">Terminal Management</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fprint_005fkeybinding"><code>rl_print_keybinding</code></a></td><td class="printindex-index-section"><a href="#Associating-Function-Names-and-Bindings">Associating Function Names and Bindings</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fprompt">rl_prompt</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fpush_005fmacro_005finput"><code>rl_push_macro_input</code></a></td><td class="printindex-index-section"><a href="#Modifying-Text">Modifying Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fread_005finit_005ffile"><code>rl_read_init_file</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fread_005fkey"><code>rl_read_key</code></a></td><td class="printindex-index-section"><a href="#Character-Input">Character Input</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005freadline_005fname">rl_readline_name</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005freadline_005fstate">rl_readline_state</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005freadline_005fversion">rl_readline_version</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fredisplay"><code>rl_redisplay</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fredisplay_005ffunction">rl_redisplay_function</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005freparse_005fcolors"><code>rl_reparse_colors</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005freplace_005fline"><code>rl_replace_line</code></a></td><td class="printindex-index-section"><a href="#Modifying-Text">Modifying Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005freset_005fafter_005fsignal"><code>rl_reset_after_signal</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005freset_005fline_005fstate"><code>rl_reset_line_state</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005freset_005fscreen_005fsize"><code>rl_reset_screen_size</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005freset_005fterminal"><code>rl_reset_terminal</code></a></td><td class="printindex-index-section"><a href="#Terminal-Management">Terminal Management</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fresize_005fterminal"><code>rl_resize_terminal</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005frestore_005fprompt"><code>rl_restore_prompt</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005frestore_005fstate"><code>rl_restore_state</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fsave_005fprompt"><code>rl_save_prompt</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fsave_005fstate"><code>rl_save_state</code></a></td><td class="printindex-index-section"><a href="#Utility-Functions">Utility Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fset_005fkey"><code>rl_set_key</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fset_005fkeyboard_005finput_005ftimeout"><code>rl_set_keyboard_input_timeout</code></a></td><td class="printindex-index-section"><a href="#Character-Input">Character Input</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fset_005fkeymap"><code>rl_set_keymap</code></a></td><td class="printindex-index-section"><a href="#Keymaps">Keymaps</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fset_005fkeymap_005fname"><code>rl_set_keymap_name</code></a></td><td class="printindex-index-section"><a href="#Keymaps">Keymaps</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fset_005fparen_005fblink_005ftimeout"><code>rl_set_paren_blink_timeout</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fset_005fprompt"><code>rl_set_prompt</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fset_005fscreen_005fsize"><code>rl_set_screen_size</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fset_005fsignals"><code>rl_set_signals</code></a></td><td class="printindex-index-section"><a href="#Readline-Signal-Handling">Readline Signal Handling</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fset_005ftimeout"><code>rl_set_timeout</code></a></td><td class="printindex-index-section"><a href="#Character-Input">Character Input</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fshow_005fchar"><code>rl_show_char</code></a></td><td class="printindex-index-section"><a href="#Redisplay">Redisplay</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fsignal_005fevent_005fhook">rl_signal_event_hook</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fsort_005fcompletion_005fmatches">rl_sort_completion_matches</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fspecial_005fprefixes">rl_special_prefixes</a></td><td class="printindex-index-section"><a href="#Completion-Variables">Completion Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fstartup_005fhook">rl_startup_hook</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fstuff_005fchar"><code>rl_stuff_char</code></a></td><td class="printindex-index-section"><a href="#Character-Input">Character Input</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fterminal_005fname">rl_terminal_name</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ftimeout_005fevent_005fhook">rl_timeout_event_hook</a></td><td class="printindex-index-section"><a href="#Readline-Variables">Readline Variables</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ftimeout_005fremaining"><code>rl_timeout_remaining</code></a></td><td class="printindex-index-section"><a href="#Character-Input">Character Input</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ftrim_005farg_005ffrom_005fkeyseq"><code>rl_trim_arg_from_keyseq</code></a></td><td class="printindex-index-section"><a href="#Associating-Function-Names-and-Bindings">Associating Function Names and Bindings</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ftty_005fset_005fdefault_005fbindings"><code>rl_tty_set_default_bindings</code></a></td><td class="printindex-index-section"><a href="#Terminal-Management">Terminal Management</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ftty_005fset_005fechoing"><code>rl_tty_set_echoing</code></a></td><td class="printindex-index-section"><a href="#Terminal-Management">Terminal Management</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005ftty_005funset_005fdefault_005fbindings"><code>rl_tty_unset_default_bindings</code></a></td><td class="printindex-index-section"><a href="#Terminal-Management">Terminal Management</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005funbind_005fcommand_005fin_005fmap"><code>rl_unbind_command_in_map</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005funbind_005ffunction_005fin_005fmap"><code>rl_unbind_function_in_map</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005funbind_005fkey"><code>rl_unbind_key</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005funbind_005fkey_005fin_005fmap"><code>rl_unbind_key_in_map</code></a></td><td class="printindex-index-section"><a href="#Binding-Keys">Binding Keys</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fusername_005fcompletion_005ffunction"><code>rl_username_completion_function</code></a></td><td class="printindex-index-section"><a href="#Completion-Functions">Completion Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fvariable_005fbind"><code>rl_variable_bind</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fvariable_005fdumper"><code>rl_variable_dumper</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-rl_005fvariable_005fvalue"><code>rl_variable_value</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Functions">Miscellaneous Functions</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-S">S</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-search_002dignore_002dcase">search-ignore-case</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-self_002dinsert-_0028a_002c-b_002c-A_002c-1_002c-_0021_002c-_2026_0029"><code>self-insert (a, b, A, 1, !, &hellip;)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-set_002dmark-_0028C_002d_0040_0029"><code>set-mark (C-@)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-show_002dall_002dif_002dambiguous">show-all-if-ambiguous</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-show_002dall_002dif_002dunmodified">show-all-if-unmodified</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-show_002dmode_002din_002dprompt">show-mode-in-prompt</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-skip_002dcompleted_002dtext">skip-completed-text</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-skip_002dcsi_002dsequence-_0028_0029"><code>skip-csi-sequence ()</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-start_002dkbd_002dmacro-_0028C_002dx-_0028_0029"><code>start-kbd-macro (C-x ()</code></a></td><td class="printindex-index-section"><a href="#Keyboard-Macros">Keyboard Macros</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-T">T</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-tab_002dinsert-_0028M_002dTAB_0029"><code>tab-insert (M-<kbd class="key">TAB</kbd>)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-tilde_002dexpand-_0028M_002d_007e_0029"><code>tilde-expand (M-~)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-transpose_002dchars-_0028C_002dt_0029"><code>transpose-chars (C-t)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-transpose_002dwords-_0028M_002dt_0029"><code>transpose-words (M-t)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-U">U</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-undo-_0028C_002d_005f-or-C_002dx-C_002du_0029"><code>undo (C-_ or C-x C-u)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-universal_002dargument-_0028_0029"><code>universal-argument ()</code></a></td><td class="printindex-index-section"><a href="#Numeric-Arguments">Numeric Arguments</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-unix_002dfilename_002drubout-_0028_0029"><code>unix-filename-rubout ()</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-unix_002dline_002ddiscard-_0028C_002du_0029"><code>unix-line-discard (C-u)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-unix_002dword_002drubout-_0028C_002dw_0029"><code>unix-word-rubout (C-w)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-upcase_002dword-_0028M_002du_0029"><code>upcase-word (M-u)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Text">Commands For Text</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-V">V</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-vi_002dcmd_002dmode_002dstring">vi-cmd-mode-string</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-vi_002dediting_002dmode-_0028M_002dC_002dj_0029"><code>vi-editing-mode (M-C-j)</code></a></td><td class="printindex-index-section"><a href="#Miscellaneous-Commands">Miscellaneous Commands</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-vi_002dins_002dmode_002dstring">vi-ins-mode-string</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-visible_002dstats">visible-stats</a></td><td class="printindex-index-section"><a href="#Readline-Init-File-Syntax">Readline Init File Syntax</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
<tr><th id="Function-and-Variable-Index_fn_letter-Y">Y</th></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-yank-_0028C_002dy_0029"><code>yank (C-y)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-yank_002dlast_002darg-_0028M_002d_002e-or-M_002d_005f_0029"><code>yank-last-arg (M-. or M-_)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-yank_002dnth_002darg-_0028M_002dC_002dy_0029"><code>yank-nth-arg (M-C-y)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-History">Commands For History</a></td></tr>
<tr><td></td><td class="printindex-index-entry"><a href="#index-yank_002dpop-_0028M_002dy_0029"><code>yank-pop (M-y)</code></a></td><td class="printindex-index-section"><a href="#Commands-For-Killing">Commands For Killing</a></td></tr>
<tr><td colspan="3"><hr></td></tr>
</table>
<table class="fn-letters-footer-printindex"><tr><th>Jump to: &nbsp; </th><td><a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_symbol-1"><b>_</b></a>
 &nbsp; 
<br>
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-A"><b>A</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-B"><b>B</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-C"><b>C</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-D"><b>D</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-E"><b>E</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-F"><b>F</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-H"><b>H</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-I"><b>I</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-K"><b>K</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-M"><b>M</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-N"><b>N</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-O"><b>O</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-P"><b>P</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-Q"><b>Q</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-R"><b>R</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-S"><b>S</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-T"><b>T</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-U"><b>U</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-V"><b>V</b></a>
 &nbsp; 
<a class="summary-letter-printindex" href="#Function-and-Variable-Index_fn_letter-Y"><b>Y</b></a>
 &nbsp; 
</td></tr></table>
</div>

</div>
</div>



</body>
</html>
