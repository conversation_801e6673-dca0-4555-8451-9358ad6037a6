{"build": "py312hca03da5_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/osx-arm64", "constrains": [], "depends": ["python >=3.12,<3.13.0a0"], "extracted_package_dir": "/Users/<USER>/miniconda3_new/pkgs/wheel-0.45.1-py312hca03da5_0", "files": ["bin/wheel", "lib/python3.12/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "lib/python3.12/site-packages/wheel-0.45.1.dist-info/METADATA", "lib/python3.12/site-packages/wheel-0.45.1.dist-info/RECORD", "lib/python3.12/site-packages/wheel-0.45.1.dist-info/WHEEL", "lib/python3.12/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "lib/python3.12/site-packages/wheel/__init__.py", "lib/python3.12/site-packages/wheel/__main__.py", "lib/python3.12/site-packages/wheel/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/__main__.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/_bdist_wheel.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/_setuptools_logging.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/bdist_wheel.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/macosx_libfile.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/metadata.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/util.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/wheelfile.cpython-312.pyc", "lib/python3.12/site-packages/wheel/_bdist_wheel.py", "lib/python3.12/site-packages/wheel/_setuptools_logging.py", "lib/python3.12/site-packages/wheel/bdist_wheel.py", "lib/python3.12/site-packages/wheel/cli/__init__.py", "lib/python3.12/site-packages/wheel/cli/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/wheel/cli/__pycache__/convert.cpython-312.pyc", "lib/python3.12/site-packages/wheel/cli/__pycache__/pack.cpython-312.pyc", "lib/python3.12/site-packages/wheel/cli/__pycache__/tags.cpython-312.pyc", "lib/python3.12/site-packages/wheel/cli/__pycache__/unpack.cpython-312.pyc", "lib/python3.12/site-packages/wheel/cli/convert.py", "lib/python3.12/site-packages/wheel/cli/pack.py", "lib/python3.12/site-packages/wheel/cli/tags.py", "lib/python3.12/site-packages/wheel/cli/unpack.py", "lib/python3.12/site-packages/wheel/macosx_libfile.py", "lib/python3.12/site-packages/wheel/metadata.py", "lib/python3.12/site-packages/wheel/util.py", "lib/python3.12/site-packages/wheel/vendored/__init__.py", "lib/python3.12/site-packages/wheel/vendored/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/LICENSE", "lib/python3.12/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "lib/python3.12/site-packages/wheel/vendored/packaging/LICENSE.BSD", "lib/python3.12/site-packages/wheel/vendored/packaging/__init__.py", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/_elffile.py", "lib/python3.12/site-packages/wheel/vendored/packaging/_manylinux.py", "lib/python3.12/site-packages/wheel/vendored/packaging/_musllinux.py", "lib/python3.12/site-packages/wheel/vendored/packaging/_parser.py", "lib/python3.12/site-packages/wheel/vendored/packaging/_structures.py", "lib/python3.12/site-packages/wheel/vendored/packaging/_tokenizer.py", "lib/python3.12/site-packages/wheel/vendored/packaging/markers.py", "lib/python3.12/site-packages/wheel/vendored/packaging/requirements.py", "lib/python3.12/site-packages/wheel/vendored/packaging/specifiers.py", "lib/python3.12/site-packages/wheel/vendored/packaging/tags.py", "lib/python3.12/site-packages/wheel/vendored/packaging/utils.py", "lib/python3.12/site-packages/wheel/vendored/packaging/version.py", "lib/python3.12/site-packages/wheel/vendored/vendor.txt", "lib/python3.12/site-packages/wheel/wheelfile.py"], "fn": "wheel-0.45.1-py312hca03da5_0.conda", "license": "MIT", "link": {"source": "/Users/<USER>/miniconda3_new/pkgs/wheel-0.45.1-py312hca03da5_0", "type": 1}, "md5": "aa66815c10f3ae9d89cf57e2be2ce571", "name": "wheel", "package_tarball_full_path": "/Users/<USER>/miniconda3_new/pkgs/wheel-0.45.1-py312hca03da5_0.conda", "paths_data": {"paths": [{"_path": "bin/wheel", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_7aou3fg_ke/croot/wheel_1737990417862/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehol", "sha256": "4b6c258712ae16d2d5b04d9fec7008f66b66a7ccda153e9137a01a1bb4d2733d", "sha256_in_prefix": "4db94375d1b905208667b024de450c6bffcaab1a7b970b0e2d112ccf30712d11", "size_in_bytes": 462}, {"_path": "lib/python3.12/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "lib/python3.12/site-packages/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "b04e96136b82c11487b79640c093a7344af76607ec497a24f4b87b2518590a60", "sha256_in_prefix": "b04e96136b82c11487b79640c093a7344af76607ec497a24f4b87b2518590a60", "size_in_bytes": 2313}, {"_path": "lib/python3.12/site-packages/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "7c8a58bf3d056258750bf926332679ec2407473a6b3f80cbb6184582f543e841", "sha256_in_prefix": "7c8a58bf3d056258750bf926332679ec2407473a6b3f80cbb6184582f543e841", "size_in_bytes": 3180}, {"_path": "lib/python3.12/site-packages/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "lib/python3.12/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "lib/python3.12/site-packages/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "lib/python3.12/site-packages/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "f43d1e9f02f6990ae708cba8d0fd684729123ac5e471a72a4cf882281548eb90", "sha256_in_prefix": "f43d1e9f02f6990ae708cba8d0fd684729123ac5e471a72a4cf882281548eb90", "size_in_bytes": 222}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/__main__.cpython-312.pyc", "path_type": "hardlink", "sha256": "83ae925453537aafbb1862c73faa934ec7fae3b2c240dec6f4dbf6730e1bbbc3", "sha256_in_prefix": "83ae925453537aafbb1862c73faa934ec7fae3b2c240dec6f4dbf6730e1bbbc3", "size_in_bytes": 957}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/_bdist_wheel.cpython-312.pyc", "path_type": "hardlink", "sha256": "e1a101ea82fa98ef23194b562657abf9d9f9b87d43edeacf2f927654807fb7ec", "sha256_in_prefix": "e1a101ea82fa98ef23194b562657abf9d9f9b87d43edeacf2f927654807fb7ec", "size_in_bytes": 25926}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/_setuptools_logging.cpython-312.pyc", "path_type": "hardlink", "sha256": "6e81b565f52e86887a28b2f3d961a6ae5566cf2d5de525c8a8ba4c3469035dd4", "sha256_in_prefix": "6e81b565f52e86887a28b2f3d961a6ae5566cf2d5de525c8a8ba4c3469035dd4", "size_in_bytes": 1368}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/bdist_wheel.cpython-312.pyc", "path_type": "hardlink", "sha256": "30cae250a057418e3ae875105681b3268e3cedc538002cfb612f5cf28ab4882c", "sha256_in_prefix": "30cae250a057418e3ae875105681b3268e3cedc538002cfb612f5cf28ab4882c", "size_in_bytes": 734}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/macosx_libfile.cpython-312.pyc", "path_type": "hardlink", "sha256": "2b43fc585f0b45b2bbc33a07118c34cbddfa7039d26eb2daca887fe4c6cf355e", "sha256_in_prefix": "2b43fc585f0b45b2bbc33a07118c34cbddfa7039d26eb2daca887fe4c6cf355e", "size_in_bytes": 16176}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/metadata.cpython-312.pyc", "path_type": "hardlink", "sha256": "a7bfdb0e50c425f25f4b097ce695b8caf577177bb5b192f58ba9cdfda3729e83", "sha256_in_prefix": "a7bfdb0e50c425f25f4b097ce695b8caf577177bb5b192f58ba9cdfda3729e83", "size_in_bytes": 8587}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/util.cpython-312.pyc", "path_type": "hardlink", "sha256": "f0676e31ea5adc76e6d70339b2ee64b28c919cd1620e64241999bebe4539a1c4", "sha256_in_prefix": "f0676e31ea5adc76e6d70339b2ee64b28c919cd1620e64241999bebe4539a1c4", "size_in_bytes": 910}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/wheelfile.cpython-312.pyc", "path_type": "hardlink", "sha256": "55b6994b3bf34bc49e961ad19cdd8002caf306f966041d072561e1751df91730", "sha256_in_prefix": "55b6994b3bf34bc49e961ad19cdd8002caf306f966041d072561e1751df91730", "size_in_bytes": 11414}, {"_path": "lib/python3.12/site-packages/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "lib/python3.12/site-packages/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "lib/python3.12/site-packages/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "lib/python3.12/site-packages/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "lib/python3.12/site-packages/wheel/cli/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "695dbad6c3cc03cb20c2f8a40c8ebdef346b625b8a2c35573b29b009f4b164c0", "sha256_in_prefix": "695dbad6c3cc03cb20c2f8a40c8ebdef346b625b8a2c35573b29b009f4b164c0", "size_in_bytes": 6885}, {"_path": "lib/python3.12/site-packages/wheel/cli/__pycache__/convert.cpython-312.pyc", "path_type": "hardlink", "sha256": "8a4fdd53dd244ae2a5c4e9ca8131f8212fc2b5c121daadd2f1ef4e37dfadd2be", "sha256_in_prefix": "8a4fdd53dd244ae2a5c4e9ca8131f8212fc2b5c121daadd2f1ef4e37dfadd2be", "size_in_bytes": 16027}, {"_path": "lib/python3.12/site-packages/wheel/cli/__pycache__/pack.cpython-312.pyc", "path_type": "hardlink", "sha256": "5f3637c3ada364005ff515766360f7f622ed3a8c921311cc77f6713e8b61f95c", "sha256_in_prefix": "5f3637c3ada364005ff515766360f7f622ed3a8c921311cc77f6713e8b61f95c", "size_in_bytes": 4408}, {"_path": "lib/python3.12/site-packages/wheel/cli/__pycache__/tags.cpython-312.pyc", "path_type": "hardlink", "sha256": "137d6a746912ad6850c89d821defba97c7d89c1f7730f7f0c6105ea1ac018bb7", "sha256_in_prefix": "137d6a746912ad6850c89d821defba97c7d89c1f7730f7f0c6105ea1ac018bb7", "size_in_bytes": 6674}, {"_path": "lib/python3.12/site-packages/wheel/cli/__pycache__/unpack.cpython-312.pyc", "path_type": "hardlink", "sha256": "2933af8c931a72e6e0e789e42e69095e3befd668fccc4a22f5a27551ecc86191", "sha256_in_prefix": "2933af8c931a72e6e0e789e42e69095e3befd668fccc4a22f5a27551ecc86191", "size_in_bytes": 1473}, {"_path": "lib/python3.12/site-packages/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "lib/python3.12/site-packages/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "lib/python3.12/site-packages/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "lib/python3.12/site-packages/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "lib/python3.12/site-packages/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "lib/python3.12/site-packages/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "lib/python3.12/site-packages/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "lib/python3.12/site-packages/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.12/site-packages/wheel/vendored/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "5b1185e2af3c88a9cfe2ce292508d846d1dec69de44ab888f597e770e04637de", "sha256_in_prefix": "5b1185e2af3c88a9cfe2ce292508d846d1dec69de44ab888f597e770e04637de", "size_in_bytes": 152}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-312.pyc", "path_type": "hardlink", "sha256": "55cb5323331f3f992e4302e462e9afd8694f99b45ad832e409f3f0421cce1b87", "sha256_in_prefix": "55cb5323331f3f992e4302e462e9afd8694f99b45ad832e409f3f0421cce1b87", "size_in_bytes": 162}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-312.pyc", "path_type": "hardlink", "sha256": "b4556db47e99fc2bb8846b524a56ba19f2f2969bdec802a70bf7886d79d0b623", "sha256_in_prefix": "b4556db47e99fc2bb8846b524a56ba19f2f2969bdec802a70bf7886d79d0b623", "size_in_bytes": 4988}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-312.pyc", "path_type": "hardlink", "sha256": "edc1c434382650116520cfe94a57c315838c0a65a74bf3362a1e7c6f0f03f702", "sha256_in_prefix": "edc1c434382650116520cfe94a57c315838c0a65a74bf3362a1e7c6f0f03f702", "size_in_bytes": 9815}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-312.pyc", "path_type": "hardlink", "sha256": "c7268baa77b7ea821751b07550355e6ad842bbb23065bbc8895c1fc4f5ec3385", "sha256_in_prefix": "c7268baa77b7ea821751b07550355e6ad842bbb23065bbc8895c1fc4f5ec3385", "size_in_bytes": 4528}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-312.pyc", "path_type": "hardlink", "sha256": "6bcf86660ba926e61b0374346f919d7a63f24d496815a1ba535dcc7067e83767", "sha256_in_prefix": "6bcf86660ba926e61b0374346f919d7a63f24d496815a1ba535dcc7067e83767", "size_in_bytes": 14018}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-312.pyc", "path_type": "hardlink", "sha256": "aa1a97d2ef131371ebb47dbc013bdd9e27303118a2adbd9e90c3e135327dd650", "sha256_in_prefix": "aa1a97d2ef131371ebb47dbc013bdd9e27303118a2adbd9e90c3e135327dd650", "size_in_bytes": 3210}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-312.pyc", "path_type": "hardlink", "sha256": "873aab52d619a007dfe532b6143e11c728ddc1245fa1e024656955fb7449c794", "sha256_in_prefix": "873aab52d619a007dfe532b6143e11c728ddc1245fa1e024656955fb7449c794", "size_in_bytes": 7910}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-312.pyc", "path_type": "hardlink", "sha256": "040db1b7bcd19c0f580a5dea6220de905bfbfc1f4cafb8a54eff7165536e3e25", "sha256_in_prefix": "040db1b7bcd19c0f580a5dea6220de905bfbfc1f4cafb8a54eff7165536e3e25", "size_in_bytes": 10481}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-312.pyc", "path_type": "hardlink", "sha256": "551426ec80e8839116b514faac16d29ce4b32e091a6e31a1835df09017975def", "sha256_in_prefix": "551426ec80e8839116b514faac16d29ce4b32e091a6e31a1835df09017975def", "size_in_bytes": 4423}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-312.pyc", "path_type": "hardlink", "sha256": "ec2e7a78bf0327f015ecebc409bd16b142e35c99ad7ba12c7b536547bce1c7bc", "sha256_in_prefix": "ec2e7a78bf0327f015ecebc409bd16b142e35c99ad7ba12c7b536547bce1c7bc", "size_in_bytes": 39493}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-312.pyc", "path_type": "hardlink", "sha256": "9747848620effddc97f55d13eef66074ea566dad92d7f8006223ce6980b6e8a5", "sha256_in_prefix": "9747848620effddc97f55d13eef66074ea566dad92d7f8006223ce6980b6e8a5", "size_in_bytes": 21621}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-312.pyc", "path_type": "hardlink", "sha256": "1064ea1bc36d4fcd15676cd4fba71e11ae09e8468d56691f70936eadc949b2af", "sha256_in_prefix": "1064ea1bc36d4fcd15676cd4fba71e11ae09e8468d56691f70936eadc949b2af", "size_in_bytes": 7255}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-312.pyc", "path_type": "hardlink", "sha256": "34f7ccc66ebc571b85533fcb2ae85da097c80c428f25e22c6a0f041a8b127cbc", "sha256_in_prefix": "34f7ccc66ebc571b85533fcb2ae85da097c80c428f25e22c6a0f041a8b127cbc", "size_in_bytes": 19971}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "lib/python3.12/site-packages/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "lib/python3.12/site-packages/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}], "paths_version": 1}, "requested_spec": "None", "sha256": "2fce5507e5df2bd093b8b53a5c50f12f979b59257f187187a3269cbf373f7556", "size": 151681, "subdir": "osx-arm64", "timestamp": 1737990467000, "url": "https://repo.anaconda.com/pkgs/main/osx-arm64/wheel-0.45.1-py312hca03da5_0.conda", "version": "0.45.1"}