/*
 * tkMacOSXCursors.h --
 *
 *	This file defines a set of Macintosh cursor resources that
 *	are only available on the Macintosh platform.
 *
 * Copyright © 1995-1996 Sun Microsystems, Inc.
 * Copyright © 2008-2009 Apple Inc.
 * Copyright © 2008-2009 <PERSON> <<EMAIL>>
 *
 * See the file "license.terms" for information on usage and redistribution
 * of this file, and for a DISCLAIMER OF ALL WARRANTIES.
 */

static const unsigned char tkMacOSXCursors[][68] = {

#define TK_MAC_CURSOR_hand 0
[TK_MAC_CURSOR_hand] = {
	0x01, 0x80, 0x1A, 0x70, 0x26, 0x48, 0x26, 0x4A, 0x12, 0x4D, 0x12, 0x49, 0x68, 0x09, 0x98, 0x01,
	0x88, 0x02, 0x40, 0x02, 0x20, 0x02, 0x20, 0x04, 0x10, 0x04, 0x08, 0x08, 0x04, 0x08, 0x04, 0x08,
	0x01, 0x80, 0x1B, 0xF0, 0x3F, 0xF8, 0x3F, 0xFA, 0x1F, 0xFF, 0x1F, 0xFF, 0x6F, 0xFF, 0xFF, 0xFF,
	0xFF, 0xFE, 0x7F, 0xFE, 0x3F, 0xFE, 0x3F, 0xFC, 0x1F, 0xFC, 0x0F, 0xF8, 0x07, 0xF8, 0x07, 0xF8,
	0x00, 0x09, 0x00, 0x08,
},

#define TK_MAC_CURSOR_bucket 2
[TK_MAC_CURSOR_bucket] = {
	0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x09, 0x80, 0x09, 0x40, 0x0B, 0x30, 0x0D, 0x18, 0x09, 0x0C,
	0x12, 0x9C, 0x21, 0x2C, 0x10, 0x4C, 0x08, 0x8C, 0x05, 0x0C, 0x02, 0x08, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x09, 0x80, 0x09, 0xC0, 0x0B, 0xF0, 0x0F, 0xF8, 0x0F, 0xFC,
	0x1F, 0xFC, 0x3F, 0xEC, 0x1F, 0xCC, 0x0F, 0x8C, 0x07, 0x0C, 0x02, 0x08, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x0D, 0x00, 0x0C,
},

#define TK_MAC_CURSOR_cancel 3
[TK_MAC_CURSOR_cancel] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x80, 0x4A, 0x40, 0x4A, 0x40, 0x3F, 0x80,
	0x0A, 0x00, 0x3F, 0x80, 0x4A, 0x40, 0x4A, 0x46, 0x31, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x80, 0x7B, 0xC0, 0xFF, 0xE0, 0xFF, 0xE0, 0x7F, 0xC0,
	0x3F, 0x80, 0x7F, 0xC0, 0xFF, 0xE6, 0xFF, 0xEF, 0x7B, 0xCF, 0x31, 0x86, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x08, 0x00, 0x05,
},

#define TK_MAC_CURSOR_resize 4
[TK_MAC_CURSOR_resize] = {
	0xFF, 0xFF, 0x80, 0x01, 0xBF, 0x01, 0xA1, 0x81, 0xA1, 0xF9, 0xA1, 0x8D, 0xA1, 0x8D, 0xBF, 0x8D,
	0x9F, 0x8D, 0x88, 0x0D, 0x88, 0x0D, 0x88, 0x0D, 0x8F, 0xFD, 0x87, 0xFD, 0x80, 0x01, 0xFF, 0xFF,
	0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x00, 0x08, 0x00, 0x08,
},

#define TK_MAC_CURSOR_eyedrop 5
[TK_MAC_CURSOR_eyedrop] = {
	0x00, 0x0E, 0x00, 0x1F, 0x00, 0x1F, 0x00, 0xFF, 0x00, 0x7E, 0x00, 0xB8, 0x01, 0x18, 0x02, 0x28,
	0x04, 0x40, 0x08, 0x80, 0x11, 0x00, 0x22, 0x00, 0x44, 0x00, 0x48, 0x00, 0xB0, 0x00, 0x40, 0x00,
	0x00, 0x0E, 0x00, 0x1F, 0x00, 0x1F, 0x00, 0xFF, 0x00, 0x7E, 0x00, 0xF8, 0x01, 0xF8, 0x03, 0xE8,
	0x07, 0xC0, 0x0F, 0x80, 0x1F, 0x00, 0x3E, 0x00, 0x7C, 0x00, 0x78, 0x00, 0xF0, 0x00, 0x40, 0x00,
	0x00, 0x0F, 0x00, 0x00,
},

#define TK_MAC_CURSOR_eyedrop_full 6
[TK_MAC_CURSOR_eyedrop_full] = {
	0x00, 0x0E, 0x00, 0x1F, 0x00, 0x1F, 0x00, 0xFF, 0x00, 0x7E, 0x00, 0xB8, 0x01, 0x18, 0x03, 0x28,
	0x07, 0xC0, 0x0F, 0x80, 0x1F, 0x00, 0x3E, 0x00, 0x7C, 0x00, 0x78, 0x00, 0xF0, 0x00, 0x40, 0x00,
	0x00, 0x0E, 0x00, 0x1F, 0x00, 0x1F, 0x00, 0xFF, 0x00, 0x7E, 0x00, 0xF8, 0x01, 0xF8, 0x03, 0xE8,
	0x07, 0xC0, 0x0F, 0x80, 0x1F, 0x00, 0x3E, 0x00, 0x7C, 0x00, 0x78, 0x00, 0xF0, 0x00, 0x40, 0x00,
	0x00, 0x0F, 0x00, 0x00,
},

#define TK_MAC_CURSOR_zoom_in 7
[TK_MAC_CURSOR_zoom_in] = {
	0x07, 0x80, 0x18, 0x60, 0x27, 0x90, 0x58, 0x68, 0x53, 0x28, 0xA3, 0x14, 0xAF, 0xD4, 0xAF, 0xD4,
	0xA3, 0x14, 0x53, 0x28, 0x58, 0x68, 0x27, 0x98, 0x18, 0x7C, 0x07, 0x8E, 0x00, 0x07, 0x00, 0x03,
	0x07, 0x80, 0x1F, 0xE0, 0x3F, 0xF0, 0x78, 0x78, 0x73, 0x38, 0xE3, 0x1C, 0xEF, 0xDC, 0xEF, 0xDC,
	0xE3, 0x1C, 0x73, 0x38, 0x78, 0x78, 0x3F, 0xF8, 0x1F, 0xFC, 0x07, 0x8E, 0x00, 0x07, 0x00, 0x03,
	0x00, 0x07, 0x00, 0x07,
},

#define TK_MAC_CURSOR_zoom_out 8
[TK_MAC_CURSOR_zoom_out] = {
	0x07, 0x80, 0x18, 0x60, 0x27, 0x90, 0x58, 0x68, 0x50, 0x28, 0xA0, 0x14, 0xAF, 0xD4, 0xAF, 0xD4,
	0xA0, 0x14, 0x50, 0x28, 0x58, 0x68, 0x27, 0x98, 0x18, 0x7C, 0x07, 0x8E, 0x00, 0x07, 0x00, 0x03,
	0x07, 0x80, 0x1F, 0xE0, 0x3F, 0xF0, 0x78, 0x78, 0x70, 0x38, 0xE0, 0x1C, 0xEF, 0xDC, 0xEF, 0xDC,
	0xE0, 0x1C, 0x70, 0x38, 0x78, 0x78, 0x3F, 0xF8, 0x1F, 0xFC, 0x07, 0x8E, 0x00, 0x07, 0x00, 0x03,
	0x00, 0x07, 0x00, 0x07,
},

};
