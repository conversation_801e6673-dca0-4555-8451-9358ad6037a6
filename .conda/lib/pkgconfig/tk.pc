# tk pkg-config source file

prefix=/Users/<USER>/pydcov/.conda
exec_prefix=/Users/<USER>/pydcov/.conda
libdir=/Users/<USER>/pydcov/.conda/lib
includedir=${prefix}/include

Name: The Tk Toolkit
Description: Tk is a cross-platform graphical user interface toolkit, the standard GUI not only for Tcl, but for many other dynamic languages as well.
URL: https://www.tcl-lang.org/
Version: 8.6.15
Requires: tcl >= 8.6
Libs: -L${libdir} -ltk8.6 -ltkstub8.6
Libs.private:  
Cflags: -I${includedir}
