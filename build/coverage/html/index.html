<!doctype html><html><head><meta name='viewport' content='width=device-width,initial-scale=1'><meta charset='UTF-8'><link rel='stylesheet' type='text/css' href='style.css'><script src='control.js'></script></head><body><h2>Coverage Report</h2><h4>Created: 2025-09-03 22:24</h4><p>Click <a href='http://clang.llvm.org/docs/SourceBasedCodeCoverage.html#interpreting-reports'>here</a> for information about interpreting this report.</p><div class='centered'><table><tr><td class='column-entry-bold'>Filename</td><td class='column-entry-bold'>Function Coverage</td><td class='column-entry-bold'>Line Coverage</td><td class='column-entry-bold'>Region Coverage</td><td class='column-entry-bold'>Branch Coverage</td></tr><tr class='light-row'><td><pre><a href='coverage/Users/<USER>/pydcov/src/algorithm.c.html'>algorithm.c</a></pre></td><td class='column-entry-red'><pre>  40.00% (2/5)</pre></td><td class='column-entry-red'><pre>  35.29% (18/51)</pre></td><td class='column-entry-red'><pre>  27.03% (20/74)</pre></td><td class='column-entry-red'><pre>  16.67% (5/30)</pre></td></tr><tr class='light-row'><td><pre><a href='coverage/Users/<USER>/pydcov/src/main.cpp.html'>main.cpp</a></pre></td><td class='column-entry-red'><pre>  66.67% (4/6)</pre></td><td class='column-entry-red'><pre>  21.74% (40/184)</pre></td><td class='column-entry-red'><pre>  23.89% (27/113)</pre></td><td class='column-entry-red'><pre>  15.28% (11/72)</pre></td></tr><tr class='light-row-bold'><td><pre>Totals</pre></td><td class='column-entry-red'><pre>  54.55% (6/11)</pre></td><td class='column-entry-red'><pre>  24.68% (58/235)</pre></td><td class='column-entry-red'><pre>  25.13% (47/187)</pre></td><td class='column-entry-red'><pre>  15.69% (16/102)</pre></td></tr></table></div><h5>Generated by llvm-cov -- llvm version 21.1.0</h5></body></html>