# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/pydcov

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/pydcov/build

# Include any dependencies generated for this target.
include CMakeFiles/algorithm.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/algorithm.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/algorithm.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/algorithm.dir/flags.make

CMakeFiles/algorithm.dir/src/algorithm.c.o: CMakeFiles/algorithm.dir/flags.make
CMakeFiles/algorithm.dir/src/algorithm.c.o: /Users/<USER>/pydcov/src/algorithm.c
CMakeFiles/algorithm.dir/src/algorithm.c.o: CMakeFiles/algorithm.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/pydcov/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/algorithm.dir/src/algorithm.c.o"
	/opt/homebrew/opt/llvm/bin/clang $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/algorithm.dir/src/algorithm.c.o -MF CMakeFiles/algorithm.dir/src/algorithm.c.o.d -o CMakeFiles/algorithm.dir/src/algorithm.c.o -c /Users/<USER>/pydcov/src/algorithm.c

CMakeFiles/algorithm.dir/src/algorithm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/algorithm.dir/src/algorithm.c.i"
	/opt/homebrew/opt/llvm/bin/clang $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/pydcov/src/algorithm.c > CMakeFiles/algorithm.dir/src/algorithm.c.i

CMakeFiles/algorithm.dir/src/algorithm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/algorithm.dir/src/algorithm.c.s"
	/opt/homebrew/opt/llvm/bin/clang $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/pydcov/src/algorithm.c -o CMakeFiles/algorithm.dir/src/algorithm.c.s

# Object files for target algorithm
algorithm_OBJECTS = \
"CMakeFiles/algorithm.dir/src/algorithm.c.o"

# External object files for target algorithm
algorithm_EXTERNAL_OBJECTS =

libalgorithm.a: CMakeFiles/algorithm.dir/src/algorithm.c.o
libalgorithm.a: CMakeFiles/algorithm.dir/build.make
libalgorithm.a: CMakeFiles/algorithm.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/pydcov/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C static library libalgorithm.a"
	$(CMAKE_COMMAND) -P CMakeFiles/algorithm.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/algorithm.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/algorithm.dir/build: libalgorithm.a
.PHONY : CMakeFiles/algorithm.dir/build

CMakeFiles/algorithm.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/algorithm.dir/cmake_clean.cmake
.PHONY : CMakeFiles/algorithm.dir/clean

CMakeFiles/algorithm.dir/depend:
	cd /Users/<USER>/pydcov/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/pydcov /Users/<USER>/pydcov /Users/<USER>/pydcov/build /Users/<USER>/pydcov/build /Users/<USER>/pydcov/build/CMakeFiles/algorithm.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/algorithm.dir/depend

