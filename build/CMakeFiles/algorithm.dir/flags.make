# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# compile C with /opt/homebrew/opt/llvm/bin/clang
C_DEFINES = 

C_INCLUDES = -I/Users/<USER>/pydcov/src

C_FLAGSarm64 =  -Wall -Wextra -pedantic -fprofile-instr-generate -fcoverage-mapping -g -std=gnu90 -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk

C_FLAGS =  -Wall -Wextra -pedantic -fprofile-instr-generate -fcoverage-mapping -g -std=gnu90 -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk

