# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/pydcov

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/pydcov/build

# Include any dependencies generated for this target.
include CMakeFiles/pydcov.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/pydcov.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/pydcov.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/pydcov.dir/flags.make

CMakeFiles/pydcov.dir/src/main.cpp.o: CMakeFiles/pydcov.dir/flags.make
CMakeFiles/pydcov.dir/src/main.cpp.o: /Users/<USER>/pydcov/src/main.cpp
CMakeFiles/pydcov.dir/src/main.cpp.o: CMakeFiles/pydcov.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/pydcov/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/pydcov.dir/src/main.cpp.o"
	/opt/homebrew/opt/llvm/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/pydcov.dir/src/main.cpp.o -MF CMakeFiles/pydcov.dir/src/main.cpp.o.d -o CMakeFiles/pydcov.dir/src/main.cpp.o -c /Users/<USER>/pydcov/src/main.cpp

CMakeFiles/pydcov.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/pydcov.dir/src/main.cpp.i"
	/opt/homebrew/opt/llvm/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/pydcov/src/main.cpp > CMakeFiles/pydcov.dir/src/main.cpp.i

CMakeFiles/pydcov.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/pydcov.dir/src/main.cpp.s"
	/opt/homebrew/opt/llvm/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/pydcov/src/main.cpp -o CMakeFiles/pydcov.dir/src/main.cpp.s

# Object files for target pydcov
pydcov_OBJECTS = \
"CMakeFiles/pydcov.dir/src/main.cpp.o"

# External object files for target pydcov
pydcov_EXTERNAL_OBJECTS =

pydcov: CMakeFiles/pydcov.dir/src/main.cpp.o
pydcov: CMakeFiles/pydcov.dir/build.make
pydcov: libalgorithm.a
pydcov: CMakeFiles/pydcov.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/pydcov/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable pydcov"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/pydcov.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/pydcov.dir/build: pydcov
.PHONY : CMakeFiles/pydcov.dir/build

CMakeFiles/pydcov.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/pydcov.dir/cmake_clean.cmake
.PHONY : CMakeFiles/pydcov.dir/clean

CMakeFiles/pydcov.dir/depend:
	cd /Users/<USER>/pydcov/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/pydcov /Users/<USER>/pydcov /Users/<USER>/pydcov/build /Users/<USER>/pydcov/build /Users/<USER>/pydcov/build/CMakeFiles/pydcov.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/pydcov.dir/depend

