# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# compile CXX with /opt/homebrew/opt/llvm/bin/clang++
CXX_DEFINES = 

CXX_INCLUDES = -I/Users/<USER>/pydcov/src

CXX_FLAGSarm64 =  -Wall -Wextra -pedantic -fprofile-instr-generate -fcoverage-mapping -g -std=gnu++11 -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk

CXX_FLAGS =  -Wall -Wextra -pedantic -fprofile-instr-generate -fcoverage-mapping -g -std=gnu++11 -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk

