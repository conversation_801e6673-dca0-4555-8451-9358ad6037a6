# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/pydcov

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/pydcov/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/algorithm.dir/all
all: CMakeFiles/pydcov.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/algorithm.dir/clean
clean: CMakeFiles/pydcov.dir/clean
clean: CMakeFiles/coverage-clean.dir/clean
clean: CMakeFiles/coverage-merge.dir/clean
clean: CMakeFiles/coverage-report.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/algorithm.dir

# All Build rule for target.
CMakeFiles/algorithm.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm.dir/build.make CMakeFiles/algorithm.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm.dir/build.make CMakeFiles/algorithm.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/pydcov/build/CMakeFiles --progress-num=1,2 "Built target algorithm"
.PHONY : CMakeFiles/algorithm.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/algorithm.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/pydcov/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/algorithm.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/pydcov/build/CMakeFiles 0
.PHONY : CMakeFiles/algorithm.dir/rule

# Convenience name for target.
algorithm: CMakeFiles/algorithm.dir/rule
.PHONY : algorithm

# clean rule for target.
CMakeFiles/algorithm.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/algorithm.dir/build.make CMakeFiles/algorithm.dir/clean
.PHONY : CMakeFiles/algorithm.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/pydcov.dir

# All Build rule for target.
CMakeFiles/pydcov.dir/all: CMakeFiles/algorithm.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pydcov.dir/build.make CMakeFiles/pydcov.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pydcov.dir/build.make CMakeFiles/pydcov.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/pydcov/build/CMakeFiles --progress-num=6,7 "Built target pydcov"
.PHONY : CMakeFiles/pydcov.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/pydcov.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/pydcov/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/pydcov.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/pydcov/build/CMakeFiles 0
.PHONY : CMakeFiles/pydcov.dir/rule

# Convenience name for target.
pydcov: CMakeFiles/pydcov.dir/rule
.PHONY : pydcov

# clean rule for target.
CMakeFiles/pydcov.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pydcov.dir/build.make CMakeFiles/pydcov.dir/clean
.PHONY : CMakeFiles/pydcov.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/coverage-clean.dir

# All Build rule for target.
CMakeFiles/coverage-clean.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage-clean.dir/build.make CMakeFiles/coverage-clean.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage-clean.dir/build.make CMakeFiles/coverage-clean.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/pydcov/build/CMakeFiles --progress-num=3 "Built target coverage-clean"
.PHONY : CMakeFiles/coverage-clean.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/coverage-clean.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/pydcov/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/coverage-clean.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/pydcov/build/CMakeFiles 0
.PHONY : CMakeFiles/coverage-clean.dir/rule

# Convenience name for target.
coverage-clean: CMakeFiles/coverage-clean.dir/rule
.PHONY : coverage-clean

# clean rule for target.
CMakeFiles/coverage-clean.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage-clean.dir/build.make CMakeFiles/coverage-clean.dir/clean
.PHONY : CMakeFiles/coverage-clean.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/coverage-merge.dir

# All Build rule for target.
CMakeFiles/coverage-merge.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage-merge.dir/build.make CMakeFiles/coverage-merge.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage-merge.dir/build.make CMakeFiles/coverage-merge.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/pydcov/build/CMakeFiles --progress-num=4 "Built target coverage-merge"
.PHONY : CMakeFiles/coverage-merge.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/coverage-merge.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/pydcov/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/coverage-merge.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/pydcov/build/CMakeFiles 0
.PHONY : CMakeFiles/coverage-merge.dir/rule

# Convenience name for target.
coverage-merge: CMakeFiles/coverage-merge.dir/rule
.PHONY : coverage-merge

# clean rule for target.
CMakeFiles/coverage-merge.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage-merge.dir/build.make CMakeFiles/coverage-merge.dir/clean
.PHONY : CMakeFiles/coverage-merge.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/coverage-report.dir

# All Build rule for target.
CMakeFiles/coverage-report.dir/all: CMakeFiles/pydcov.dir/all
CMakeFiles/coverage-report.dir/all: CMakeFiles/coverage-merge.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage-report.dir/build.make CMakeFiles/coverage-report.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage-report.dir/build.make CMakeFiles/coverage-report.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/pydcov/build/CMakeFiles --progress-num=5 "Built target coverage-report"
.PHONY : CMakeFiles/coverage-report.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/coverage-report.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/pydcov/build/CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/coverage-report.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/pydcov/build/CMakeFiles 0
.PHONY : CMakeFiles/coverage-report.dir/rule

# Convenience name for target.
coverage-report: CMakeFiles/coverage-report.dir/rule
.PHONY : coverage-report

# clean rule for target.
CMakeFiles/coverage-report.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/coverage-report.dir/build.make CMakeFiles/coverage-report.dir/clean
.PHONY : CMakeFiles/coverage-report.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

