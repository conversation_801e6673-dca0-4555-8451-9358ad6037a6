# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/pydcov

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/pydcov/build

# Utility rule file for coverage-clean.

# Include any custom commands dependencies for this target.
include CMakeFiles/coverage-clean.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/coverage-clean.dir/progress.make

CMakeFiles/coverage-clean:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/pydcov/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Cleaning coverage data"
	find /Users/<USER>/pydcov/build -name *.profraw -delete
	find /Users/<USER>/pydcov/build -name *.profdata -delete

coverage-clean: CMakeFiles/coverage-clean
coverage-clean: CMakeFiles/coverage-clean.dir/build.make
.PHONY : coverage-clean

# Rule to build all files generated by this target.
CMakeFiles/coverage-clean.dir/build: coverage-clean
.PHONY : CMakeFiles/coverage-clean.dir/build

CMakeFiles/coverage-clean.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/coverage-clean.dir/cmake_clean.cmake
.PHONY : CMakeFiles/coverage-clean.dir/clean

CMakeFiles/coverage-clean.dir/depend:
	cd /Users/<USER>/pydcov/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/pydcov /Users/<USER>/pydcov /Users/<USER>/pydcov/build /Users/<USER>/pydcov/build /Users/<USER>/pydcov/build/CMakeFiles/coverage-clean.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/coverage-clean.dir/depend

