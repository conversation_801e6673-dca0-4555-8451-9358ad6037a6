
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Darwin - 24.6.0 - arm64
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /opt/homebrew/opt/llvm/bin/clang 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is Clang, found in:
        /Users/<USER>/pydcov/build/CMakeFiles/3.30.1/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /opt/homebrew/opt/llvm/bin/clang++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is Clang, found in:
        /Users/<USER>/pydcov/build/CMakeFiles/3.30.1/CompilerIdCXX/a.out
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-uNOmph"
      binary: "/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-uNOmph"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "/opt/homebrew/Cellar/llvm/21.1.0/bin/clang-scan-deps"
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-uNOmph'
        
        Run Build Command(s): /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_beb2e/fast
        /Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_beb2e.dir/build.make CMakeFiles/cmTC_beb2e.dir/build
        Building C object CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o
        /opt/homebrew/opt/llvm/bin/clang   -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk   -v -Wl,-v -MD -MT CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeCCompilerABI.c
        Homebrew clang version 21.1.0
        Target: arm64-apple-darwin24.6.0
        Thread model: posix
        InstalledDir: /opt/homebrew/Cellar/llvm/21.1.0/bin
        Configuration file: /opt/homebrew/etc/clang/arm64-apple-darwin24.cfg
        System configuration file directory: /opt/homebrew/etc/clang
        User configuration file directory: /Users/<USER>/.config/clang
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         (in-process)
         "/opt/homebrew/Cellar/llvm/21.1.0/bin/clang-21" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Werror=undef-prefix -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -funwind-tables=1 -target-sdk-version=15.5 -fcompatibility-qualified-id-block-type-checking -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-modulemap-allow-subdirectory-search -enable-tlsdesc -target-cpu apple-m1 -target-feature +v8.4a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +ccpp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +flagm -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-uNOmph -target-linker-version 1167.4.1 -v -fcoverage-compilation-dir=/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-uNOmph -resource-dir /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21 -dependency-file CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/local/include -internal-isystem /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/SubFrameworks -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/Library/Frameworks -ferror-limit 19 -stack-protector 1 -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fmax-type-align=16 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 21.1.0 based upon LLVM 21.1.0 default target arm64-apple-darwin24.6.0
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/SubFrameworks"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking C executable cmTC_beb2e
        /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E cmake_link_script CMakeFiles/cmTC_beb2e.dir/link.txt --verbose=1
        /opt/homebrew/opt/llvm/bin/clang  -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -o cmTC_beb2e
        Homebrew clang version 21.1.0
        Target: arm64-apple-darwin24.6.0
        Thread model: posix
        InstalledDir: /opt/homebrew/Cellar/llvm/21.1.0/bin
        Configuration file: /opt/homebrew/etc/clang/arm64-apple-darwin24.cfg
        System configuration file directory: /opt/homebrew/etc/clang
        User configuration file directory: /Users/<USER>/.config/clang
         "/usr/bin/ld" -demangle -lto_library /opt/homebrew/Cellar/llvm/21.1.0/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_beb2e -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -lSystem /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1167.5
        BUILD 01:45:05 Apr 30 2025
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 21.1.0 (static support for 29, runtime is 30)
        TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
        Library search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib/swift
        Framework search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:113 (message)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include]
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include]
        end of search list found
        collapse include dir [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include] ==> [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include]
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include]
        implicit include dirs: [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include;/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-uNOmph']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_beb2e/fast]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_beb2e.dir/build.make CMakeFiles/cmTC_beb2e.dir/build]
        ignore line: [Building C object CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o]
        ignore line: [/opt/homebrew/opt/llvm/bin/clang   -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk   -v -Wl -v -MD -MT CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Homebrew clang version 21.1.0]
        ignore line: [Target: arm64-apple-darwin24.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /opt/homebrew/Cellar/llvm/21.1.0/bin]
        ignore line: [Configuration file: /opt/homebrew/etc/clang/arm64-apple-darwin24.cfg]
        ignore line: [System configuration file directory: /opt/homebrew/etc/clang]
        ignore line: [User configuration file directory: /Users/<USER>/.config/clang]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ (in-process)]
        ignore line: [ "/opt/homebrew/Cellar/llvm/21.1.0/bin/clang-21" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Werror=undef-prefix -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -funwind-tables=1 -target-sdk-version=15.5 -fcompatibility-qualified-id-block-type-checking -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-modulemap-allow-subdirectory-search -enable-tlsdesc -target-cpu apple-m1 -target-feature +v8.4a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +ccpp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +flagm -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-uNOmph -target-linker-version 1167.4.1 -v -fcoverage-compilation-dir=/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-uNOmph -resource-dir /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21 -dependency-file CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/local/include -internal-isystem /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/SubFrameworks -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/Library/Frameworks -ferror-limit 19 -stack-protector 1 -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fmax-type-align=16 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 21.1.0 based upon LLVM 21.1.0 default target arm64-apple-darwin24.6.0]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/SubFrameworks"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking C executable cmTC_beb2e]
        ignore line: [/opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E cmake_link_script CMakeFiles/cmTC_beb2e.dir/link.txt --verbose=1]
        ignore line: [/opt/homebrew/opt/llvm/bin/clang  -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -Wl -search_paths_first -Wl -headerpad_max_install_names  -v -Wl -v CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -o cmTC_beb2e]
        ignore line: [Homebrew clang version 21.1.0]
        ignore line: [Target: arm64-apple-darwin24.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /opt/homebrew/Cellar/llvm/21.1.0/bin]
        ignore line: [Configuration file: /opt/homebrew/etc/clang/arm64-apple-darwin24.cfg]
        ignore line: [System configuration file directory: /opt/homebrew/etc/clang]
        ignore line: [User configuration file directory: /Users/<USER>/.config/clang]
        link line: [ "/usr/bin/ld" -demangle -lto_library /opt/homebrew/Cellar/llvm/21.1.0/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_beb2e -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o -lSystem /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/lib/darwin/libclang_rt.osx.a]
          arg [/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/opt/homebrew/Cellar/llvm/21.1.0/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.5] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_beb2e] ==> ignore
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_beb2e.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/lib/darwin/libclang_rt.osx.a] ==> lib [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/lib/darwin/libclang_rt.osx.a]
        linker tool for 'C': /usr/bin/ld
        Library search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib/swift]
        Framework search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib/swift] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib/swift]
        collapse framework dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib/swift]
        implicit fwks: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1167.5
      BUILD 01:45:05 Apr 30 2025
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 17.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-nBSvXw"
      binary: "/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-nBSvXw"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "/opt/homebrew/Cellar/llvm/21.1.0/bin/clang-scan-deps"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-nBSvXw'
        
        Run Build Command(s): /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_4276c/fast
        /Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_4276c.dir/build.make CMakeFiles/cmTC_4276c.dir/build
        Building CXX object CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o
        /opt/homebrew/opt/llvm/bin/clang++   -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk   -v -Wl,-v -MD -MT CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        Homebrew clang version 21.1.0
        Target: arm64-apple-darwin24.6.0
        Thread model: posix
        InstalledDir: /opt/homebrew/Cellar/llvm/21.1.0/bin
        Configuration file: /opt/homebrew/etc/clang/arm64-apple-darwin24.cfg
        System configuration file directory: /opt/homebrew/etc/clang
        User configuration file directory: /Users/<USER>/.config/clang
        clang++: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         (in-process)
         "/opt/homebrew/Cellar/llvm/21.1.0/bin/clang-21" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Werror=undef-prefix -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -funwind-tables=1 -target-sdk-version=15.5 -fcompatibility-qualified-id-block-type-checking -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-modulemap-allow-subdirectory-search -enable-tlsdesc -target-cpu apple-m1 -target-feature +v8.4a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +ccpp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +flagm -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-nBSvXw -target-linker-version 1167.4.1 -v -fcoverage-compilation-dir=/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-nBSvXw -resource-dir /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21 -dependency-file CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -internal-isystem /opt/homebrew/Cellar/llvm/21.1.0/bin/../include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/local/include -internal-isystem /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/SubFrameworks -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/Library/Frameworks -fdeprecated-macro -ferror-limit 19 -stack-protector 1 -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -fmax-type-align=16 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 21.1.0 based upon LLVM 21.1.0 default target arm64-apple-darwin24.6.0
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/SubFrameworks"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /opt/homebrew/Cellar/llvm/21.1.0/bin/../include/c++/v1
         /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking CXX executable cmTC_4276c
        /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E cmake_link_script CMakeFiles/cmTC_4276c.dir/link.txt --verbose=1
        /opt/homebrew/opt/llvm/bin/clang++  -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_4276c
        Homebrew clang version 21.1.0
        Target: arm64-apple-darwin24.6.0
        Thread model: posix
        InstalledDir: /opt/homebrew/Cellar/llvm/21.1.0/bin
        Configuration file: /opt/homebrew/etc/clang/arm64-apple-darwin24.cfg
        System configuration file directory: /opt/homebrew/etc/clang
        User configuration file directory: /Users/<USER>/.config/clang
         "/usr/bin/ld" -demangle -lto_library /opt/homebrew/Cellar/llvm/21.1.0/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_4276c -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1167.5
        BUILD 01:45:05 Apr 30 2025
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 21.1.0 (static support for 29, runtime is 30)
        TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
        Library search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib/swift
        Framework search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:113 (message)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/opt/homebrew/Cellar/llvm/21.1.0/bin/../include/c++/v1]
          add: [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include]
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include]
        end of search list found
        collapse include dir [/opt/homebrew/Cellar/llvm/21.1.0/bin/../include/c++/v1] ==> [/opt/homebrew/Cellar/llvm/21.1.0/include/c++/v1]
        collapse include dir [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include] ==> [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include]
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include]
        implicit include dirs: [/opt/homebrew/Cellar/llvm/21.1.0/include/c++/v1;/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include;/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-nBSvXw']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_4276c/fast]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_4276c.dir/build.make CMakeFiles/cmTC_4276c.dir/build]
        ignore line: [Building CXX object CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/opt/homebrew/opt/llvm/bin/clang++   -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk   -v -Wl -v -MD -MT CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Homebrew clang version 21.1.0]
        ignore line: [Target: arm64-apple-darwin24.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /opt/homebrew/Cellar/llvm/21.1.0/bin]
        ignore line: [Configuration file: /opt/homebrew/etc/clang/arm64-apple-darwin24.cfg]
        ignore line: [System configuration file directory: /opt/homebrew/etc/clang]
        ignore line: [User configuration file directory: /Users/<USER>/.config/clang]
        ignore line: [clang++: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ (in-process)]
        ignore line: [ "/opt/homebrew/Cellar/llvm/21.1.0/bin/clang-21" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Werror=undef-prefix -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -ffp-contract=on -fno-rounding-math -funwind-tables=1 -target-sdk-version=15.5 -fcompatibility-qualified-id-block-type-checking -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-modulemap-allow-subdirectory-search -enable-tlsdesc -target-cpu apple-m1 -target-feature +v8.4a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +ccpp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +flagm -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-nBSvXw -target-linker-version 1167.4.1 -v -fcoverage-compilation-dir=/Users/<USER>/pydcov/build/CMakeFiles/CMakeScratch/TryCompile-nBSvXw -resource-dir /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21 -dependency-file CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -internal-isystem /opt/homebrew/Cellar/llvm/21.1.0/bin/../include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/local/include -internal-isystem /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/SubFrameworks -internal-iframework /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/Library/Frameworks -fdeprecated-macro -ferror-limit 19 -stack-protector 1 -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -fmax-type-align=16 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 21.1.0 based upon LLVM 21.1.0 default target arm64-apple-darwin24.6.0]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/SubFrameworks"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /opt/homebrew/Cellar/llvm/21.1.0/bin/../include/c++/v1]
        ignore line: [ /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking CXX executable cmTC_4276c]
        ignore line: [/opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E cmake_link_script CMakeFiles/cmTC_4276c.dir/link.txt --verbose=1]
        ignore line: [/opt/homebrew/opt/llvm/bin/clang++  -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -Wl -search_paths_first -Wl -headerpad_max_install_names  -v -Wl -v CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_4276c]
        ignore line: [Homebrew clang version 21.1.0]
        ignore line: [Target: arm64-apple-darwin24.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /opt/homebrew/Cellar/llvm/21.1.0/bin]
        ignore line: [Configuration file: /opt/homebrew/etc/clang/arm64-apple-darwin24.cfg]
        ignore line: [System configuration file directory: /opt/homebrew/etc/clang]
        ignore line: [User configuration file directory: /Users/<USER>/.config/clang]
        link line: [ "/usr/bin/ld" -demangle -lto_library /opt/homebrew/Cellar/llvm/21.1.0/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_4276c -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/lib/darwin/libclang_rt.osx.a]
          arg [/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/opt/homebrew/Cellar/llvm/21.1.0/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.5] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_4276c] ==> ignore
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_4276c.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lc++] ==> lib [c++]
          arg [-lSystem] ==> lib [System]
          arg [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/lib/darwin/libclang_rt.osx.a] ==> lib [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/lib/darwin/libclang_rt.osx.a]
        linker tool for 'CXX': /usr/bin/ld
        Library search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib/swift]
        Framework search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/opt/homebrew/Cellar/llvm/21.1.0/lib/clang/21/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib/swift] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib/swift]
        collapse framework dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks]
        implicit libs: [c++]
        implicit objs: []
        implicit dirs: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/usr/lib/swift]
        implicit fwks: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.5.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "/opt/homebrew/Cellar/cmake/3.30.1/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1167.5
      BUILD 01:45:05 Apr 30 2025
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 17.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
...
