# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/Cellar/cmake/3.30.1/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/pydcov

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/pydcov/build

# Utility rule file for coverage-report.

# Include any custom commands dependencies for this target.
include CMakeFiles/coverage-report.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/coverage-report.dir/progress.make

CMakeFiles/coverage-report: pydcov
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/pydcov/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating coverage report with /opt/homebrew/opt/llvm/bin/llvm-cov"
	/opt/homebrew/opt/llvm/bin/llvm-cov show /Users/<USER>/pydcov/build/pydcov -instr-profile=/Users/<USER>/pydcov/build/coverage/coverage.profdata -format=html -output-dir=/Users/<USER>/pydcov/build/coverage/html
	/opt/homebrew/opt/llvm/bin/llvm-cov export /Users/<USER>/pydcov/build/pydcov -instr-profile=/Users/<USER>/pydcov/build/coverage/coverage.profdata -format=lcov > /Users/<USER>/pydcov/build/coverage/coverage.info

coverage-report: CMakeFiles/coverage-report
coverage-report: CMakeFiles/coverage-report.dir/build.make
.PHONY : coverage-report

# Rule to build all files generated by this target.
CMakeFiles/coverage-report.dir/build: coverage-report
.PHONY : CMakeFiles/coverage-report.dir/build

CMakeFiles/coverage-report.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/coverage-report.dir/cmake_clean.cmake
.PHONY : CMakeFiles/coverage-report.dir/clean

CMakeFiles/coverage-report.dir/depend:
	cd /Users/<USER>/pydcov/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/pydcov /Users/<USER>/pydcov /Users/<USER>/pydcov/build /Users/<USER>/pydcov/build /Users/<USER>/pydcov/build/CMakeFiles/coverage-report.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/coverage-report.dir/depend

